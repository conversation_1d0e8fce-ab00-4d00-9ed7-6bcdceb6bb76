#!/usr/bin/env python3
"""
Comprehensive Log Analysis Tool for Epinnox v6
Analyzes trading system logs to identify critical issues preventing autonomous trading execution
"""

import sys
import os
import re
import json
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LogAnalysisTool:
    """Comprehensive log analysis tool for identifying trading system issues"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logs_dir = self.project_root / 'logs'
        self.analysis_results = {}
        self.critical_issues = []
        self.warnings = []
        self.recommendations = []
        
        logger.info("🔍 Log Analysis Tool initialized")
    
    def analyze_all_logs(self) -> Dict:
        """Analyze all log files to identify critical issues"""
        print("🔍 COMPREHENSIVE LOG ANALYSIS FOR AUTONOMOUS TRADING ISSUES")
        print("=" * 70)
        
        analysis_start = time.time()
        
        # Analyze different log categories
        self._analyze_main_logs()
        self._analyze_error_logs()
        self._analyze_autonomous_trading_logs()
        self._analyze_deployment_records()
        self._analyze_system_logs()
        
        # Identify critical patterns
        self._identify_critical_patterns()
        
        # Generate comprehensive report
        analysis_time = time.time() - analysis_start
        self._generate_analysis_report(analysis_time)
        
        return self.analysis_results
    
    def _analyze_main_logs(self):
        """Analyze main application logs"""
        print("\n📋 ANALYZING MAIN APPLICATION LOGS")
        print("-" * 50)
        
        main_log_files = list(self.logs_dir.glob("epinnox_*.log"))
        
        if not main_log_files:
            print("   ❌ No main log files found")
            self.critical_issues.append("No main log files found")
            return
        
        # Get the most recent log file
        latest_log = max(main_log_files, key=lambda f: f.stat().st_mtime)
        print(f"   📄 Analyzing: {latest_log.name}")
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # Analyze log patterns
            startup_count = len(re.findall(r'Epinnox v6 starting up', log_content))
            llm_requests = len(re.findall(r'LMStudio Request', log_content))
            trading_decisions = len(re.findall(r'DECISION: (LONG|SHORT|WAIT)', log_content))
            order_executions = len(re.findall(r'Order executed|Trade executed', log_content))
            
            print(f"   📊 System startups: {startup_count}")
            print(f"   🧠 LLM requests: {llm_requests}")
            print(f"   📈 Trading decisions: {trading_decisions}")
            print(f"   💰 Order executions: {order_executions}")
            
            # Check for critical issues
            if startup_count > 5:
                self.critical_issues.append(f"Excessive restarts detected: {startup_count} startups")
            
            if llm_requests > 0 and trading_decisions == 0:
                self.critical_issues.append("LLM making requests but no trading decisions found")
            
            if trading_decisions > 0 and order_executions == 0:
                self.critical_issues.append("Trading decisions made but no order executions found")
            
            # Check for autonomous trading activity
            autonomous_patterns = re.findall(r'autonomous.*trading|ScalperGPT.*Auto.*Trader', log_content, re.IGNORECASE)
            if not autonomous_patterns:
                self.critical_issues.append("No autonomous trading activity detected in logs")
            
            self.analysis_results['main_logs'] = {
                'file': latest_log.name,
                'startup_count': startup_count,
                'llm_requests': llm_requests,
                'trading_decisions': trading_decisions,
                'order_executions': order_executions,
                'autonomous_activity': len(autonomous_patterns)
            }
            
        except Exception as e:
            print(f"   ❌ Error analyzing main logs: {e}")
            self.critical_issues.append(f"Main log analysis failed: {e}")
    
    def _analyze_error_logs(self):
        """Analyze error logs for critical failures"""
        print("\n📋 ANALYZING ERROR LOGS")
        print("-" * 50)
        
        error_log_files = list(self.logs_dir.glob("epinnox_errors_*.log"))
        
        if not error_log_files:
            print("   ✅ No error log files found")
            return
        
        # Get the most recent error log
        latest_error_log = max(error_log_files, key=lambda f: f.stat().st_mtime)
        print(f"   📄 Analyzing: {latest_error_log.name}")
        
        try:
            with open(latest_error_log, 'r', encoding='utf-8') as f:
                error_content = f.read()
            
            if not error_content.strip():
                print("   ✅ No errors in recent error log")
                return
            
            # Analyze error patterns
            error_lines = [line.strip() for line in error_content.split('\n') if line.strip()]
            
            error_categories = defaultdict(int)
            for line in error_lines:
                if 'websocket' in line.lower():
                    error_categories['WebSocket Errors'] += 1
                elif 'api' in line.lower():
                    error_categories['API Errors'] += 1
                elif 'trading' in line.lower():
                    error_categories['Trading Errors'] += 1
                elif 'connection' in line.lower():
                    error_categories['Connection Errors'] += 1
                else:
                    error_categories['Other Errors'] += 1
            
            print(f"   📊 Total error lines: {len(error_lines)}")
            for category, count in error_categories.items():
                print(f"   ❌ {category}: {count}")
                if count > 0:
                    self.critical_issues.append(f"{category}: {count} occurrences")
            
            # Check for specific critical errors
            if 'wrapped C/C++ object' in error_content:
                self.critical_issues.append("Memory management issues detected (wrapped C/C++ object deleted)")
            
            self.analysis_results['error_logs'] = {
                'file': latest_error_log.name,
                'total_errors': len(error_lines),
                'error_categories': dict(error_categories),
                'recent_errors': error_lines[-5:] if error_lines else []
            }
            
        except Exception as e:
            print(f"   ❌ Error analyzing error logs: {e}")
            self.critical_issues.append(f"Error log analysis failed: {e}")
    
    def _analyze_autonomous_trading_logs(self):
        """Analyze autonomous trading specific logs"""
        print("\n📋 ANALYZING AUTONOMOUS TRADING LOGS")
        print("-" * 50)
        
        autonomous_log = self.logs_dir / 'autonomous_trading.log'
        
        if not autonomous_log.exists():
            print("   ❌ Autonomous trading log file not found")
            self.critical_issues.append("Autonomous trading log file missing")
            return
        
        try:
            with open(autonomous_log, 'r', encoding='utf-8') as f:
                autonomous_content = f.read()
            
            if not autonomous_content.strip():
                print("   ❌ Autonomous trading log is empty")
                self.critical_issues.append("Autonomous trading log is empty - no autonomous activity recorded")
                return
            
            # Analyze autonomous trading patterns
            autonomous_lines = [line.strip() for line in autonomous_content.split('\n') if line.strip()]
            
            print(f"   📊 Autonomous log entries: {len(autonomous_lines)}")
            
            # Look for specific autonomous trading events
            trade_entries = len([line for line in autonomous_lines if 'trade' in line.lower()])
            position_entries = len([line for line in autonomous_lines if 'position' in line.lower()])
            decision_entries = len([line for line in autonomous_lines if 'decision' in line.lower()])
            
            print(f"   💰 Trade entries: {trade_entries}")
            print(f"   📊 Position entries: {position_entries}")
            print(f"   🧠 Decision entries: {decision_entries}")
            
            if trade_entries == 0:
                self.critical_issues.append("No trade entries in autonomous trading log")
            
            self.analysis_results['autonomous_logs'] = {
                'file': 'autonomous_trading.log',
                'total_entries': len(autonomous_lines),
                'trade_entries': trade_entries,
                'position_entries': position_entries,
                'decision_entries': decision_entries
            }
            
        except Exception as e:
            print(f"   ❌ Error analyzing autonomous trading logs: {e}")
            self.critical_issues.append(f"Autonomous trading log analysis failed: {e}")
    
    def _analyze_deployment_records(self):
        """Analyze deployment records"""
        print("\n📋 ANALYZING DEPLOYMENT RECORDS")
        print("-" * 50)
        
        deployment_files = list(self.logs_dir.glob("deployment_record_*.json"))
        
        if not deployment_files:
            print("   ❌ No deployment records found")
            self.critical_issues.append("No deployment records found")
            return
        
        # Get the most recent deployment record
        latest_deployment = max(deployment_files, key=lambda f: f.stat().st_mtime)
        print(f"   📄 Analyzing: {latest_deployment.name}")
        
        try:
            with open(latest_deployment, 'r', encoding='utf-8') as f:
                deployment_data = json.load(f)
            
            deployment_type = deployment_data.get('deployment_type', 'unknown')
            status = deployment_data.get('status', 'unknown')
            initial_balance = deployment_data.get('initial_balance', 0)
            
            print(f"   🚀 Deployment type: {deployment_type}")
            print(f"   📊 Status: {status}")
            print(f"   💰 Initial balance: ${initial_balance}")
            
            # Check safety checks
            safety_checks = deployment_data.get('safety_checks_passed', {})
            failed_checks = [check for check, passed in safety_checks.items() if not passed]
            
            if failed_checks:
                print(f"   ❌ Failed safety checks: {', '.join(failed_checks)}")
                self.critical_issues.append(f"Failed safety checks: {', '.join(failed_checks)}")
            else:
                print("   ✅ All safety checks passed")
            
            # Check if deployment was completed
            if status == 'prepared' and deployment_type == 'autonomous_live':
                self.critical_issues.append("Deployment prepared but not executed - autonomous trading not started")
            
            self.analysis_results['deployment'] = {
                'file': latest_deployment.name,
                'type': deployment_type,
                'status': status,
                'initial_balance': initial_balance,
                'safety_checks_passed': all(safety_checks.values()),
                'failed_checks': failed_checks
            }
            
        except Exception as e:
            print(f"   ❌ Error analyzing deployment records: {e}")
            self.critical_issues.append(f"Deployment record analysis failed: {e}")
    
    def _analyze_system_logs(self):
        """Analyze system logs"""
        print("\n📋 ANALYZING SYSTEM LOGS")
        print("-" * 50)
        
        system_logs_dir = self.logs_dir / 'system'
        
        if not system_logs_dir.exists():
            print("   ⚠️ System logs directory not found")
            return
        
        system_log_files = list(system_logs_dir.glob("*.log"))
        
        if not system_log_files:
            print("   ⚠️ No system log files found")
            return
        
        # Get the most recent system log
        latest_system_log = max(system_log_files, key=lambda f: f.stat().st_mtime)
        print(f"   📄 Analyzing: {latest_system_log.name}")
        
        try:
            with open(latest_system_log, 'r', encoding='utf-8') as f:
                system_content = f.read()
            
            # Check for system validation results
            if 'SYSTEM FULLY READY' in system_content:
                print("   ✅ System validation: FULLY READY")
            elif 'SYSTEM MOSTLY READY' in system_content:
                print("   🟡 System validation: MOSTLY READY")
                self.warnings.append("System validation shows warnings")
            elif 'SYSTEM NEEDS ATTENTION' in system_content:
                print("   ❌ System validation: NEEDS ATTENTION")
                self.critical_issues.append("System validation failed")
            
            self.analysis_results['system_logs'] = {
                'file': latest_system_log.name,
                'validation_status': 'ready' if 'FULLY READY' in system_content else 'issues'
            }
            
        except Exception as e:
            print(f"   ❌ Error analyzing system logs: {e}")
            self.warnings.append(f"System log analysis failed: {e}")
    
    def _identify_critical_patterns(self):
        """Identify critical patterns across all logs"""
        print("\n📋 IDENTIFYING CRITICAL PATTERNS")
        print("-" * 50)
        
        # Pattern 1: LLM making decisions but no trades executed
        main_logs = self.analysis_results.get('main_logs', {})
        if main_logs.get('trading_decisions', 0) > 0 and main_logs.get('order_executions', 0) == 0:
            self.critical_issues.append("CRITICAL: Trading decisions made but no orders executed")
            print("   ❌ CRITICAL: Trading decisions made but no orders executed")
        
        # Pattern 2: System prepared but not executing
        deployment = self.analysis_results.get('deployment', {})
        if deployment.get('status') == 'prepared' and deployment.get('type') == 'autonomous_live':
            self.critical_issues.append("CRITICAL: System prepared for autonomous trading but not executing")
            print("   ❌ CRITICAL: System prepared for autonomous trading but not executing")
        
        # Pattern 3: Empty autonomous trading logs
        autonomous_logs = self.analysis_results.get('autonomous_logs', {})
        if autonomous_logs.get('total_entries', 0) == 0:
            self.critical_issues.append("CRITICAL: No autonomous trading activity recorded")
            print("   ❌ CRITICAL: No autonomous trading activity recorded")
        
        # Pattern 4: Excessive system restarts
        if main_logs.get('startup_count', 0) > 5:
            self.critical_issues.append("WARNING: Excessive system restarts detected")
            print("   ⚠️ WARNING: Excessive system restarts detected")
        
        # Pattern 5: WebSocket connection issues
        error_logs = self.analysis_results.get('error_logs', {})
        websocket_errors = error_logs.get('error_categories', {}).get('WebSocket Errors', 0)
        if websocket_errors > 0:
            self.critical_issues.append(f"WARNING: WebSocket connection issues ({websocket_errors} errors)")
            print(f"   ⚠️ WARNING: WebSocket connection issues ({websocket_errors} errors)")
    
    def _generate_analysis_report(self, analysis_time: float):
        """Generate comprehensive analysis report"""
        print(f"\n🎯 COMPREHENSIVE LOG ANALYSIS REPORT")
        print("=" * 70)
        
        print(f"\n📊 ANALYSIS STATISTICS:")
        print(f"   ⏱️ Analysis Time: {analysis_time:.2f}s")
        print(f"   📄 Log Categories Analyzed: {len(self.analysis_results)}")
        print(f"   ❌ Critical Issues: {len(self.critical_issues)}")
        print(f"   ⚠️ Warnings: {len(self.warnings)}")
        
        print(f"\n🚨 CRITICAL ISSUES PREVENTING AUTONOMOUS TRADING:")
        if self.critical_issues:
            for i, issue in enumerate(self.critical_issues, 1):
                print(f"   {i}. ❌ {issue}")
        else:
            print("   ✅ No critical issues detected")
        
        if self.warnings:
            print(f"\n⚠️ WARNINGS:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. ⚠️ {warning}")
        
        # Generate recommendations
        self._generate_recommendations()
        
        if self.recommendations:
            print(f"\n💡 RECOMMENDATIONS TO FIX AUTONOMOUS TRADING:")
            for i, recommendation in enumerate(self.recommendations, 1):
                print(f"   {i}. 🔧 {recommendation}")
        
        # Overall assessment
        if not self.critical_issues:
            print(f"\n🟢 OVERALL ASSESSMENT: SYSTEM OPERATIONAL")
            print("   ✅ No critical issues preventing autonomous trading")
        elif len(self.critical_issues) <= 2:
            print(f"\n🟡 OVERALL ASSESSMENT: MINOR ISSUES DETECTED")
            print("   🔧 Address critical issues to enable autonomous trading")
        else:
            print(f"\n🔴 OVERALL ASSESSMENT: MAJOR ISSUES DETECTED")
            print("   🛠️ Multiple critical issues preventing autonomous trading")
        
        # Save analysis report
        self._save_analysis_report()
    
    def _generate_recommendations(self):
        """Generate specific recommendations based on identified issues"""
        
        # Check for specific issue patterns and generate targeted recommendations
        for issue in self.critical_issues:
            if "Trading decisions made but no orders executed" in issue:
                self.recommendations.append("Check order execution system and exchange connectivity")
                self.recommendations.append("Verify trading permissions and API credentials")
                self.recommendations.append("Review risk management settings that might block orders")
            
            elif "System prepared for autonomous trading but not executing" in issue:
                self.recommendations.append("Start autonomous trading execution after preparation")
                self.recommendations.append("Check if autonomous trading checkbox is enabled in GUI")
                self.recommendations.append("Verify autonomous trading loop is running")
            
            elif "No autonomous trading activity recorded" in issue:
                self.recommendations.append("Enable autonomous trading mode in the application")
                self.recommendations.append("Check autonomous trading configuration settings")
                self.recommendations.append("Verify LLM integration is working properly")
            
            elif "WebSocket connection issues" in issue:
                self.recommendations.append("Check network connectivity and firewall settings")
                self.recommendations.append("Restart WebSocket connections")
                self.recommendations.append("Verify exchange API endpoints are accessible")
            
            elif "Excessive restarts" in issue:
                self.recommendations.append("Investigate system stability issues")
                self.recommendations.append("Check for memory leaks or resource exhaustion")
                self.recommendations.append("Review error logs for crash causes")
        
        # Add general recommendations if no specific ones generated
        if not self.recommendations:
            self.recommendations.append("Review system configuration and restart application")
            self.recommendations.append("Check all dependencies and system requirements")
            self.recommendations.append("Verify exchange API credentials and permissions")
    
    def _save_analysis_report(self):
        """Save analysis report to file"""
        try:
            report_dir = self.logs_dir / 'analysis'
            report_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"log_analysis_{timestamp}.json"
            
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'analysis_results': self.analysis_results,
                'critical_issues': self.critical_issues,
                'warnings': self.warnings,
                'recommendations': self.recommendations
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"\n📄 Analysis report saved: {report_file}")
            
        except Exception as e:
            print(f"⚠️ Could not save analysis report: {e}")

def main():
    """Main execution for log analysis"""
    import time
    
    analyzer = LogAnalysisTool()
    results = analyzer.analyze_all_logs()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
