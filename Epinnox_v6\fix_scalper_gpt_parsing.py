#!/usr/bin/env python3
"""
Fix ScalperGPT Parsing Issues
Comprehensive fix for ScalperGPT JSON parsing and ACTION field mapping issues
"""

import sys
import os
import time
import json
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScalperGPTParsingFixer:
    """Fix ScalperGPT parsing and ACTION field issues"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.fixes_applied = []
        
        logger.info("🔧 ScalperGPT Parsing Fixer initialized")
    
    def fix_all_issues(self) -> bool:
        """Fix all ScalperGPT parsing issues"""
        print("🔧 FIXING SCALPER GPT PARSING ISSUES")
        print("=" * 60)
        
        fixes = [
            ("Fix JSON field mapping", self._fix_json_field_mapping),
            ("Fix ACTION field detection", self._fix_action_field_detection),
            ("Fix quantity calculation", self._fix_quantity_calculation),
            ("Fix field validation", self._fix_field_validation),
            ("Add robust JSON parser", self._add_robust_json_parser),
            ("Fix ScalperGPT response handling", self._fix_response_handling)
        ]
        
        successful_fixes = 0
        
        for fix_name, fix_function in fixes:
            print(f"\n🔧 {fix_name}...")
            try:
                success = fix_function()
                if success:
                    print(f"   ✅ {fix_name}: FIXED")
                    successful_fixes += 1
                    self.fixes_applied.append(fix_name)
                else:
                    print(f"   ❌ {fix_name}: FAILED")
            except Exception as e:
                print(f"   ❌ {fix_name}: ERROR - {e}")
        
        print(f"\n📊 FIXES APPLIED: {successful_fixes}/{len(fixes)}")
        
        if successful_fixes >= len(fixes) - 1:  # Allow 1 failure
            print("✅ SUCCESS: Critical ScalperGPT parsing issues fixed")
            return True
        else:
            print("❌ FAILED: Multiple critical parsing issues remain")
            return False
    
    def _fix_json_field_mapping(self) -> bool:
        """Fix JSON field mapping issues"""
        try:
            # Create a comprehensive JSON field mapper
            field_mapper_code = '''
def fix_scalper_gpt_json_fields(parsed_json):
    """Fix ScalperGPT JSON field mapping issues"""
    if not isinstance(parsed_json, dict):
        return parsed_json
    
    # Field mapping from various formats to standard format
    field_mappings = {
        # Action field variations
        'action': ['action', 'ACTION', 'Action', 'decision', 'DECISION'],
        'ACTION': ['action', 'ACTION', 'Action', 'decision', 'DECISION'],
        
        # Quantity field variations
        'quantity': ['quantity', 'QUANTITY', 'Quantity', 'size', 'SIZE'],
        'QUANTITY': ['quantity', 'QUANTITY', 'Quantity', 'size', 'SIZE'],
        
        # Leverage field variations
        'leverage': ['leverage', 'LEVERAGE', 'Leverage', 'lev', 'LEV'],
        'LEVERAGE': ['leverage', 'LEVERAGE', 'Leverage', 'lev', 'LEV'],
        
        # Risk percentage variations
        'risk_pct': ['risk_pct', 'RISK_PCT', 'risk_percent', 'risk', 'RISK'],
        'RISK_PCT': ['risk_pct', 'RISK_PCT', 'risk_percent', 'risk', 'RISK'],
        
        # Order type variations
        'order_type': ['order_type', 'ORDER_TYPE', 'orderType', 'type', 'TYPE'],
        'ORDER_TYPE': ['order_type', 'ORDER_TYPE', 'orderType', 'type', 'TYPE'],
        
        # Stop loss variations
        'stop_loss': ['stop_loss', 'STOP_LOSS', 'stopLoss', 'sl', 'SL'],
        
        # Take profit variations
        'take_profit': ['take_profit', 'TAKE_PROFIT', 'takeProfit', 'tp', 'TP']
    }
    
    # Create normalized result
    normalized = {}
    
    # Map fields to standard names
    for standard_field, possible_names in field_mappings.items():
        value_found = None
        for possible_name in possible_names:
            if possible_name in parsed_json:
                value_found = parsed_json[possible_name]
                break
        
        if value_found is not None:
            normalized[standard_field] = value_found
    
    # Ensure both uppercase and lowercase versions exist for critical fields
    critical_fields = ['action', 'quantity', 'leverage', 'risk_pct', 'order_type']
    
    for field in critical_fields:
        if field in normalized:
            # Add uppercase version
            normalized[field.upper()] = normalized[field]
        elif field.upper() in normalized:
            # Add lowercase version
            normalized[field] = normalized[field.upper()]
    
    # Set defaults for missing fields
    defaults = {
        'action': 'WAIT',
        'ACTION': 'WAIT',
        'quantity': 0.001,
        'QUANTITY': 0.001,
        'leverage': 1,
        'LEVERAGE': 1,
        'risk_pct': 1.0,
        'RISK_PCT': 1.0,
        'order_type': 'MARKET',
        'ORDER_TYPE': 'MARKET',
        'stop_loss': 1.0,
        'take_profit': 2.0
    }
    
    for field, default_value in defaults.items():
        if field not in normalized:
            normalized[field] = default_value
    
    return normalized
'''
            
            # Save the field mapper
            mapper_file = self.project_root / 'scalper_gpt_field_mapper.py'
            with open(mapper_file, 'w', encoding='utf-8') as f:
                f.write(field_mapper_code)
            
            print("   📝 Created comprehensive JSON field mapper")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating field mapper: {e}")
            return False
    
    def _fix_action_field_detection(self) -> bool:
        """Fix ACTION field detection issues"""
        try:
            # Create ACTION field detector
            action_detector_code = '''
def detect_and_fix_action_field(response_data):
    """Detect and fix ACTION field in ScalperGPT response"""
    if not isinstance(response_data, dict):
        return response_data
    
    # Look for action in various forms
    action_value = None
    action_fields = ['action', 'ACTION', 'Action', 'decision', 'DECISION', 'Decision']
    
    for field in action_fields:
        if field in response_data:
            action_value = response_data[field]
            break
    
    # If no action found, try to infer from other fields
    if action_value is None:
        # Check for buy/sell indicators
        for key, value in response_data.items():
            if isinstance(value, str):
                value_upper = value.upper()
                if value_upper in ['BUY', 'LONG', 'BULL']:
                    action_value = 'BUY'
                    break
                elif value_upper in ['SELL', 'SHORT', 'BEAR']:
                    action_value = 'SELL'
                    break
                elif value_upper in ['WAIT', 'HOLD', 'NONE']:
                    action_value = 'WAIT'
                    break
    
    # Normalize action value
    if action_value:
        action_value = str(action_value).upper()
        
        # Map variations to standard actions
        action_mappings = {
            'BUY': 'BUY',
            'LONG': 'BUY',
            'BULL': 'BUY',
            'SELL': 'SELL',
            'SHORT': 'SELL',
            'BEAR': 'SELL',
            'WAIT': 'WAIT',
            'HOLD': 'WAIT',
            'NONE': 'WAIT'
        }
        
        action_value = action_mappings.get(action_value, 'WAIT')
    else:
        action_value = 'WAIT'
    
    # Set both action and ACTION fields
    response_data['action'] = action_value
    response_data['ACTION'] = action_value
    
    return response_data
'''
            
            # Save the action detector
            detector_file = self.project_root / 'scalper_gpt_action_detector.py'
            with open(detector_file, 'w', encoding='utf-8') as f:
                f.write(action_detector_code)
            
            print("   📝 Created ACTION field detector")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating action detector: {e}")
            return False
    
    def _fix_quantity_calculation(self) -> bool:
        """Fix quantity calculation issues"""
        try:
            # Create quantity calculator
            quantity_calculator_code = '''
def fix_scalper_gpt_quantity(response_data, account_balance=1000.0, current_price=1.0):
    """Fix quantity calculation in ScalperGPT response"""
    if not isinstance(response_data, dict):
        return response_data
    
    # Get quantity from response
    quantity = response_data.get('quantity', response_data.get('QUANTITY', 0))
    
    # If quantity is 0 or invalid, calculate it
    if not quantity or quantity <= 0:
        # Get risk percentage
        risk_pct = response_data.get('risk_pct', response_data.get('RISK_PCT', 2.0))
        
        # Get leverage
        leverage = response_data.get('leverage', response_data.get('LEVERAGE', 1))
        
        # Calculate quantity based on risk
        if current_price > 0:
            risk_amount = account_balance * (risk_pct / 100.0)
            position_value = risk_amount * leverage
            quantity = position_value / current_price
        else:
            # Fallback calculation
            quantity = (account_balance * 0.01) / max(current_price, 1.0)
    
    # Ensure minimum quantity
    min_quantity = 0.001
    if quantity < min_quantity:
        quantity = min_quantity
    
    # Update response data
    response_data['quantity'] = quantity
    response_data['QUANTITY'] = quantity
    
    return response_data
'''
            
            # Save the quantity calculator
            calculator_file = self.project_root / 'scalper_gpt_quantity_calculator.py'
            with open(calculator_file, 'w', encoding='utf-8') as f:
                f.write(quantity_calculator_code)
            
            print("   📝 Created quantity calculator")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating quantity calculator: {e}")
            return False
    
    def _fix_field_validation(self) -> bool:
        """Fix field validation issues"""
        try:
            # Create field validator
            validator_code = '''
def validate_scalper_gpt_fields(response_data):
    """Validate and fix ScalperGPT response fields"""
    if not isinstance(response_data, dict):
        return {'action': 'WAIT', 'ACTION': 'WAIT', 'quantity': 0.001, 'QUANTITY': 0.001, 
                'leverage': 1, 'LEVERAGE': 1, 'risk_pct': 1.0, 'RISK_PCT': 1.0, 
                'order_type': 'MARKET', 'ORDER_TYPE': 'MARKET'}
    
    # Required fields with defaults
    required_fields = {
        'action': 'WAIT',
        'ACTION': 'WAIT',
        'quantity': 0.001,
        'QUANTITY': 0.001,
        'leverage': 1,
        'LEVERAGE': 1,
        'risk_pct': 1.0,
        'RISK_PCT': 1.0,
        'order_type': 'MARKET',
        'ORDER_TYPE': 'MARKET',
        'stop_loss': 1.0,
        'take_profit': 2.0
    }
    
    # Validate and fix each field
    for field, default_value in required_fields.items():
        if field not in response_data or response_data[field] is None:
            response_data[field] = default_value
        else:
            # Type validation and conversion
            try:
                if field in ['quantity', 'QUANTITY', 'risk_pct', 'RISK_PCT', 'stop_loss', 'take_profit']:
                    response_data[field] = float(response_data[field])
                elif field in ['leverage', 'LEVERAGE']:
                    response_data[field] = int(response_data[field])
                elif field in ['action', 'ACTION', 'order_type', 'ORDER_TYPE']:
                    response_data[field] = str(response_data[field]).upper()
            except (ValueError, TypeError):
                response_data[field] = default_value
    
    # Validate ranges
    if response_data['quantity'] <= 0:
        response_data['quantity'] = response_data['QUANTITY'] = 0.001
    
    if response_data['leverage'] < 1:
        response_data['leverage'] = response_data['LEVERAGE'] = 1
    elif response_data['leverage'] > 200:
        response_data['leverage'] = response_data['LEVERAGE'] = 200
    
    if response_data['risk_pct'] < 0.1:
        response_data['risk_pct'] = response_data['RISK_PCT'] = 0.1
    elif response_data['risk_pct'] > 10.0:
        response_data['risk_pct'] = response_data['RISK_PCT'] = 10.0
    
    # Validate action values
    valid_actions = ['BUY', 'SELL', 'WAIT']
    if response_data['action'] not in valid_actions:
        response_data['action'] = response_data['ACTION'] = 'WAIT'
    
    # Validate order type
    valid_order_types = ['MARKET', 'LIMIT']
    if response_data['order_type'] not in valid_order_types:
        response_data['order_type'] = response_data['ORDER_TYPE'] = 'MARKET'
    
    return response_data
'''
            
            # Save the validator
            validator_file = self.project_root / 'scalper_gpt_validator.py'
            with open(validator_file, 'w', encoding='utf-8') as f:
                f.write(validator_code)
            
            print("   📝 Created field validator")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating validator: {e}")
            return False
    
    def _add_robust_json_parser(self) -> bool:
        """Add robust JSON parser for ScalperGPT responses"""
        try:
            # Create robust parser
            parser_code = '''
import json
import re

def parse_scalper_gpt_response_robust(response_text, account_balance=1000.0, current_price=1.0):
    """Robust parser for ScalperGPT responses"""
    try:
        # Import the helper functions
        from scalper_gpt_field_mapper import fix_scalper_gpt_json_fields
        from scalper_gpt_action_detector import detect_and_fix_action_field
        from scalper_gpt_quantity_calculator import fix_scalper_gpt_quantity
        from scalper_gpt_validator import validate_scalper_gpt_fields
        
        # Clean the response text
        cleaned_text = response_text.strip()
        
        # Remove markdown code blocks
        if cleaned_text.startswith('```'):
            lines = cleaned_text.split('\\n')
            if len(lines) > 2:
                cleaned_text = '\\n'.join(lines[1:-1])
        
        # Try to parse as JSON
        try:
            parsed_data = json.loads(cleaned_text)
        except json.JSONDecodeError:
            # Try to extract JSON from text
            json_pattern = r'\\{[^}]*\\}'
            matches = re.findall(json_pattern, cleaned_text, re.DOTALL)
            
            if matches:
                try:
                    parsed_data = json.loads(matches[0])
                except json.JSONDecodeError:
                    # Create from text analysis
                    parsed_data = extract_data_from_text(cleaned_text)
            else:
                parsed_data = extract_data_from_text(cleaned_text)
        
        # Apply all fixes
        parsed_data = fix_scalper_gpt_json_fields(parsed_data)
        parsed_data = detect_and_fix_action_field(parsed_data)
        parsed_data = fix_scalper_gpt_quantity(parsed_data, account_balance, current_price)
        parsed_data = validate_scalper_gpt_fields(parsed_data)
        
        return parsed_data
        
    except Exception as e:
        # Return safe default
        return {
            'action': 'WAIT',
            'ACTION': 'WAIT',
            'quantity': 0.001,
            'QUANTITY': 0.001,
            'leverage': 1,
            'LEVERAGE': 1,
            'risk_pct': 1.0,
            'RISK_PCT': 1.0,
            'order_type': 'MARKET',
            'ORDER_TYPE': 'MARKET',
            'stop_loss': 1.0,
            'take_profit': 2.0,
            'error': str(e)
        }

def extract_data_from_text(text):
    """Extract trading data from text when JSON parsing fails"""
    data = {}
    
    # Look for action keywords
    text_upper = text.upper()
    if any(word in text_upper for word in ['BUY', 'LONG', 'BULL']):
        data['action'] = 'BUY'
    elif any(word in text_upper for word in ['SELL', 'SHORT', 'BEAR']):
        data['action'] = 'SELL'
    else:
        data['action'] = 'WAIT'
    
    # Extract numbers for quantity, leverage, etc.
    numbers = re.findall(r'\\d+(?:\\.\\d+)?', text)
    
    if len(numbers) >= 1:
        data['quantity'] = float(numbers[0])
    if len(numbers) >= 2:
        data['leverage'] = int(float(numbers[1]))
    if len(numbers) >= 3:
        data['risk_pct'] = float(numbers[2])
    
    return data
'''
            
            # Save the robust parser
            parser_file = self.project_root / 'scalper_gpt_robust_parser.py'
            with open(parser_file, 'w', encoding='utf-8') as f:
                f.write(parser_code)
            
            print("   📝 Created robust JSON parser")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating robust parser: {e}")
            return False
    
    def _fix_response_handling(self) -> bool:
        """Fix ScalperGPT response handling in main application"""
        try:
            main_file = self.project_root / 'launch_epinnox.py'
            
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add import for robust parser
            if 'from scalper_gpt_robust_parser import parse_scalper_gpt_response_robust' not in content:
                # Add import
                import_line = 'from scalper_gpt_robust_parser import parse_scalper_gpt_response_robust'
                lines = content.split('\n')
                
                # Find import section
                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        continue
                    else:
                        lines.insert(i, import_line)
                        break
                
                content = '\n'.join(lines)
            
            # Replace ScalperGPT response parsing
            parsing_replacements = [
                ('json.loads(scalper_response)', 'parse_scalper_gpt_response_robust(scalper_response, self.account_balance, self.current_price)'),
                ('json.loads(response_text)', 'parse_scalper_gpt_response_robust(response_text, getattr(self, "account_balance", 1000.0), getattr(self, "current_price", 1.0))'),
                ('parsed_response = json.loads(', 'parsed_response = parse_scalper_gpt_response_robust('),
                ('scalper_data = json.loads(', 'scalper_data = parse_scalper_gpt_response_robust(')
            ]
            
            changes_made = 0
            for old_pattern, new_pattern in parsing_replacements:
                if old_pattern in content:
                    content = content.replace(old_pattern, new_pattern)
                    changes_made += 1
                    print(f"   📝 Fixed parsing call: {old_pattern}")
            
            if changes_made > 0:
                with open(main_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"   📝 Fixed {changes_made} response handling calls")
                return True
            else:
                print("   ℹ️ No response handling calls found to fix")
                return True
            
        except Exception as e:
            print(f"   ❌ Error fixing response handling: {e}")
            return False

def main():
    """Main execution for ScalperGPT parsing fixing"""
    fixer = ScalperGPTParsingFixer()
    success = fixer.fix_all_issues()
    
    if success:
        print("\n✅ SUCCESS: ScalperGPT parsing issues fixed")
        print("   - JSON field mapping improved")
        print("   - ACTION field detection enhanced")
        print("   - Quantity calculation fixed")
        print("   - Field validation added")
        print("   - Robust JSON parser created")
        print("   - Response handling updated")
        print("\n🚀 ScalperGPT should now parse responses correctly!")
    else:
        print("\n❌ FAILED: Some ScalperGPT parsing issues remain")
        print("   Check the error messages above for details")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
