#!/usr/bin/env python3
"""
Final System Validator for Epinnox v6
Comprehensive system validation without subprocess dependencies
"""

import sys
import os
import time
import logging
import psutil
import platform
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalSystemValidator:
    """Final comprehensive system validator for Epinnox v6"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.validation_results = {}
        self.startup_time = time.time()
        
        logger.info("🎯 Final System Validator initialized")
    
    def run_final_validation(self) -> bool:
        """Run final comprehensive system validation"""
        print("🎯 EPINNOX v6 FINAL SYSTEM VALIDATION")
        print("=" * 60)
        
        validation_start = time.time()
        
        # Run all validation checks
        checks = [
            ("System Requirements", self._check_system_requirements),
            ("Python Environment", self._check_python_environment),
            ("Package Dependencies", self._check_package_dependencies),
            ("Directory Structure", self._check_directory_structure),
            ("Configuration Files", self._check_configuration_files),
            ("Network Connectivity", self._check_network_connectivity),
            ("Component Integration", self._check_component_integration),
            ("Trading Readiness", self._check_trading_readiness),
            ("Performance Metrics", self._check_performance_metrics),
            ("Security Validation", self._check_security_validation)
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        for check_name, check_function in checks:
            print(f"\n🔍 {check_name}...")
            try:
                success = check_function()
                if success:
                    print(f"   ✅ {check_name}: PASSED")
                    passed_checks += 1
                else:
                    print(f"   ❌ {check_name}: FAILED")
            except Exception as e:
                print(f"   ❌ {check_name}: ERROR - {e}")
                self.validation_results[check_name] = {
                    'status': 'FAIL',
                    'message': f'Check failed: {e}'
                }
        
        # Calculate validation time
        validation_time = time.time() - validation_start
        
        # Generate final report
        overall_success = self._generate_final_report(passed_checks, total_checks, validation_time)
        
        return overall_success
    
    def _check_system_requirements(self) -> bool:
        """Check basic system requirements"""
        try:
            # Check RAM
            ram_gb = psutil.virtual_memory().total / (1024**3)
            ram_ok = ram_gb >= 4.0
            
            # Check CPU cores
            cpu_cores = psutil.cpu_count(logical=False)
            cpu_ok = cpu_cores >= 2
            
            # Check disk space
            disk_usage = psutil.disk_usage(str(self.project_root))
            free_gb = disk_usage.free / (1024**3)
            disk_ok = free_gb >= 1.0
            
            # Check platform
            platform_ok = platform.system() in ['Windows', 'Linux', 'Darwin']
            
            all_ok = ram_ok and cpu_ok and disk_ok and platform_ok
            
            self.validation_results['System Requirements'] = {
                'status': 'PASS' if all_ok else 'FAIL',
                'message': f'RAM: {ram_gb:.1f}GB, CPU: {cpu_cores} cores, Disk: {free_gb:.1f}GB',
                'details': {
                    'ram_gb': ram_gb,
                    'cpu_cores': cpu_cores,
                    'free_gb': free_gb,
                    'platform': platform.system()
                }
            }
            
            return all_ok
            
        except Exception as e:
            self.validation_results['System Requirements'] = {
                'status': 'FAIL',
                'message': f'System check failed: {e}'
            }
            return False
    
    def _check_python_environment(self) -> bool:
        """Check Python environment"""
        try:
            # Check Python version
            python_version = sys.version_info[:3]
            version_ok = python_version >= (3, 8, 0)
            
            # Check Python executable
            python_exe = sys.executable
            exe_ok = os.path.exists(python_exe)
            
            # Check site-packages
            import site
            site_packages = site.getsitepackages()
            site_ok = len(site_packages) > 0
            
            all_ok = version_ok and exe_ok and site_ok
            
            self.validation_results['Python Environment'] = {
                'status': 'PASS' if all_ok else 'FAIL',
                'message': f'Python {".".join(map(str, python_version))} at {python_exe}',
                'details': {
                    'version': python_version,
                    'executable': python_exe,
                    'site_packages': site_packages
                }
            }
            
            return all_ok
            
        except Exception as e:
            self.validation_results['Python Environment'] = {
                'status': 'FAIL',
                'message': f'Python environment check failed: {e}'
            }
            return False
    
    def _check_package_dependencies(self) -> bool:
        """Check critical package dependencies"""
        try:
            critical_packages = [
                'PyQt5', 'ccxt', 'pandas', 'numpy', 'requests', 
                'pyyaml', 'psutil', 'asyncio', 'websockets', 'aiohttp'
            ]
            
            missing_packages = []
            available_packages = []
            
            for package in critical_packages:
                try:
                    __import__(package)
                    available_packages.append(package)
                except ImportError:
                    missing_packages.append(package)
            
            all_ok = len(missing_packages) == 0
            
            self.validation_results['Package Dependencies'] = {
                'status': 'PASS' if all_ok else 'FAIL',
                'message': f'{len(available_packages)}/{len(critical_packages)} packages available',
                'details': {
                    'available': available_packages,
                    'missing': missing_packages
                }
            }
            
            return all_ok
            
        except Exception as e:
            self.validation_results['Package Dependencies'] = {
                'status': 'FAIL',
                'message': f'Package dependency check failed: {e}'
            }
            return False
    
    def _check_directory_structure(self) -> bool:
        """Check required directory structure"""
        try:
            required_dirs = [
                'logs', 'cache', 'data', 'config', 'screenshots', 
                'test_results', 'backups', 'temp', 'exports'
            ]
            
            missing_dirs = []
            existing_dirs = []
            
            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    existing_dirs.append(dir_name)
                else:
                    missing_dirs.append(dir_name)
                    # Try to create missing directory
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        existing_dirs.append(dir_name)
                        missing_dirs.remove(dir_name)
                    except:
                        pass
            
            all_ok = len(missing_dirs) == 0
            
            self.validation_results['Directory Structure'] = {
                'status': 'PASS' if all_ok else 'FAIL',
                'message': f'{len(existing_dirs)}/{len(required_dirs)} directories available',
                'details': {
                    'existing': existing_dirs,
                    'missing': missing_dirs
                }
            }
            
            return all_ok
            
        except Exception as e:
            self.validation_results['Directory Structure'] = {
                'status': 'FAIL',
                'message': f'Directory structure check failed: {e}'
            }
            return False
    
    def _check_configuration_files(self) -> bool:
        """Check configuration files"""
        try:
            config_files = [
                'config/config.yaml',
                'config/trading_config.py',
                'credentials.py'
            ]
            
            missing_files = []
            existing_files = []
            
            for config_file in config_files:
                file_path = self.project_root / config_file
                if file_path.exists():
                    existing_files.append(config_file)
                else:
                    missing_files.append(config_file)
            
            # Configuration is OK if at least trading_config.py exists
            essential_ok = (self.project_root / 'config/trading_config.py').exists()
            
            self.validation_results['Configuration Files'] = {
                'status': 'PASS' if essential_ok else 'FAIL',
                'message': f'{len(existing_files)}/{len(config_files)} config files available',
                'details': {
                    'existing': existing_files,
                    'missing': missing_files
                }
            }
            
            return essential_ok
            
        except Exception as e:
            self.validation_results['Configuration Files'] = {
                'status': 'FAIL',
                'message': f'Configuration file check failed: {e}'
            }
            return False
    
    def _check_network_connectivity(self) -> bool:
        """Check network connectivity"""
        try:
            import requests
            
            # Test basic internet connectivity
            try:
                response = requests.get('https://httpbin.org/status/200', timeout=10)
                internet_ok = response.status_code == 200
            except:
                internet_ok = False
            
            # Test exchange connectivity
            exchange_endpoints = [
                ('Binance', 'https://api.binance.com/api/v3/ping'),
                ('OKX', 'https://www.okx.com/api/v5/public/time')
            ]
            
            accessible_exchanges = []
            for name, url in exchange_endpoints:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        accessible_exchanges.append(name)
                except:
                    pass
            
            # Network is OK if internet works and at least one exchange is accessible
            network_ok = internet_ok and len(accessible_exchanges) > 0
            
            self.validation_results['Network Connectivity'] = {
                'status': 'PASS' if network_ok else 'FAIL',
                'message': f'Internet: {"OK" if internet_ok else "FAIL"}, Exchanges: {len(accessible_exchanges)}/2',
                'details': {
                    'internet': internet_ok,
                    'accessible_exchanges': accessible_exchanges
                }
            }
            
            return network_ok
            
        except Exception as e:
            self.validation_results['Network Connectivity'] = {
                'status': 'FAIL',
                'message': f'Network connectivity check failed: {e}'
            }
            return False
    
    def _check_component_integration(self) -> bool:
        """Check component integration"""
        try:
            critical_components = [
                'launch_epinnox',
                'symbol_scanner',
                'core.llm_orchestrator',
                'portfolio.portfolio_manager',
                'core.risk_management_system',
                'trading.ccxt_trading_engine'
            ]
            
            failed_imports = []
            successful_imports = []
            
            for component in critical_components:
                try:
                    __import__(component)
                    successful_imports.append(component)
                except ImportError:
                    failed_imports.append(component)
            
            integration_ok = len(failed_imports) == 0
            
            self.validation_results['Component Integration'] = {
                'status': 'PASS' if integration_ok else 'FAIL',
                'message': f'{len(successful_imports)}/{len(critical_components)} components integrated',
                'details': {
                    'successful': successful_imports,
                    'failed': failed_imports
                }
            }
            
            return integration_ok
            
        except Exception as e:
            self.validation_results['Component Integration'] = {
                'status': 'FAIL',
                'message': f'Component integration check failed: {e}'
            }
            return False
    
    def _check_trading_readiness(self) -> bool:
        """Check trading system readiness"""
        try:
            # Check credentials
            cred_path = self.project_root / 'credentials.py'
            credentials_ok = cred_path.exists()
            
            # Check trading config
            trading_config_path = self.project_root / 'config/trading_config.py'
            trading_config_ok = trading_config_path.exists()
            
            # Check log directory
            log_dir = self.project_root / 'logs'
            log_dir_ok = log_dir.exists()
            
            # Check data directory
            data_dir = self.project_root / 'data'
            data_dir_ok = data_dir.exists()
            
            trading_ready = credentials_ok and trading_config_ok and log_dir_ok and data_dir_ok
            
            self.validation_results['Trading Readiness'] = {
                'status': 'PASS' if trading_ready else 'FAIL',
                'message': f'Trading system {"ready" if trading_ready else "not ready"}',
                'details': {
                    'credentials': credentials_ok,
                    'trading_config': trading_config_ok,
                    'log_dir': log_dir_ok,
                    'data_dir': data_dir_ok
                }
            }
            
            return trading_ready
            
        except Exception as e:
            self.validation_results['Trading Readiness'] = {
                'status': 'FAIL',
                'message': f'Trading readiness check failed: {e}'
            }
            return False
    
    def _check_performance_metrics(self) -> bool:
        """Check system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_ok = cpu_percent < 80
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_ok = memory.percent < 90
            
            # Disk I/O
            try:
                disk_io = psutil.disk_io_counters()
                disk_ok = True
            except:
                disk_ok = False
            
            performance_ok = cpu_ok and memory_ok and disk_ok
            
            self.validation_results['Performance Metrics'] = {
                'status': 'PASS' if performance_ok else 'WARNING',
                'message': f'CPU: {cpu_percent:.1f}%, RAM: {memory.percent:.1f}%',
                'details': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'disk_io_available': disk_ok
                }
            }
            
            return performance_ok
            
        except Exception as e:
            self.validation_results['Performance Metrics'] = {
                'status': 'FAIL',
                'message': f'Performance metrics check failed: {e}'
            }
            return False
    
    def _check_security_validation(self) -> bool:
        """Check security validation"""
        try:
            # Check file permissions
            critical_files = ['credentials.py', 'config/trading_config.py']
            permission_issues = []
            
            for file_name in critical_files:
                file_path = self.project_root / file_name
                if file_path.exists():
                    if not os.access(file_path, os.R_OK):
                        permission_issues.append(f"{file_name}: No read permission")
            
            # Check for running instances
            current_pid = os.getpid()
            conflicting_processes = []
            
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        if proc.info['cmdline']:
                            cmdline = ' '.join(proc.info['cmdline'])
                            if 'epinnox' in cmdline.lower() and proc.info['pid'] != current_pid:
                                conflicting_processes.append(proc.info['pid'])
            except:
                pass
            
            security_ok = len(permission_issues) == 0 and len(conflicting_processes) == 0
            
            self.validation_results['Security Validation'] = {
                'status': 'PASS' if security_ok else 'WARNING',
                'message': f'Security {"validated" if security_ok else "warnings detected"}',
                'details': {
                    'permission_issues': permission_issues,
                    'conflicting_processes': conflicting_processes
                }
            }
            
            return security_ok
            
        except Exception as e:
            self.validation_results['Security Validation'] = {
                'status': 'FAIL',
                'message': f'Security validation failed: {e}'
            }
            return False
    
    def _generate_final_report(self, passed_checks: int, total_checks: int, validation_time: float) -> bool:
        """Generate final validation report"""
        print(f"\n🎯 FINAL VALIDATION REPORT")
        print("=" * 60)
        
        # Calculate success rate
        success_rate = (passed_checks / total_checks) * 100
        
        print(f"\n📊 VALIDATION STATISTICS:")
        print(f"   ⏱️ Validation Time: {validation_time:.2f}s")
        print(f"   📋 Total Checks: {total_checks}")
        print(f"   ✅ Passed: {passed_checks}")
        print(f"   ❌ Failed: {total_checks - passed_checks}")
        print(f"   📈 Success Rate: {success_rate:.1f}%")
        
        # Detailed results
        print(f"\n📋 DETAILED RESULTS:")
        for check_name, result in self.validation_results.items():
            status_icon = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌"}[result['status']]
            print(f"   {status_icon} {check_name}: {result['message']}")
        
        # Final determination
        critical_failures = sum(1 for r in self.validation_results.values() if r['status'] == 'FAIL')
        
        if critical_failures == 0:
            if success_rate >= 90:
                final_status = "🟢 SYSTEM FULLY READY"
                ready = True
            else:
                final_status = "🟡 SYSTEM MOSTLY READY"
                ready = True
        else:
            final_status = "🔴 SYSTEM NEEDS ATTENTION"
            ready = False
        
        print(f"\n🚀 FINAL STATUS: {final_status}")
        
        if ready:
            print("   ✅ Epinnox v6 is ready for deployment")
            print("   💰 System validated for autonomous trading")
            print("   🎯 Proceed with confidence")
        else:
            print("   ❌ Critical issues detected")
            print("   🛠️ Address failures before deployment")
            print("   📋 Review detailed results above")
        
        return ready

def main():
    """Main execution for final system validation"""
    validator = FinalSystemValidator()
    success = validator.run_final_validation()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
