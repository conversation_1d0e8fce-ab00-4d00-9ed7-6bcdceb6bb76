#!/usr/bin/env python3
"""
Comprehensive Startup Validation Suite
Complete system validation before launching Epinnox v6 trading system
"""

import sys
import os
import time
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StartupValidationSuite:
    """Comprehensive startup validation for Epinnox v6"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.validation_results = {}
        self.startup_time = time.time()
        
        logger.info("🚀 Startup Validation Suite initialized")
    
    def run_complete_validation(self) -> bool:
        """Run complete startup validation suite"""
        print("🚀 EPINNOX v6 COMPREHENSIVE STARTUP VALIDATION SUITE")
        print("=" * 70)
        
        validation_start = time.time()
        
        # Phase 1: System Dependencies
        print("\n📋 PHASE 1: SYSTEM DEPENDENCY VALIDATION")
        print("-" * 50)
        dep_success = self._run_dependency_checks()
        
        # Phase 2: Configuration Validation
        print("\n📋 PHASE 2: CONFIGURATION VALIDATION")
        print("-" * 50)
        config_success = self._run_configuration_validation()
        
        # Phase 3: Component Integration Tests
        print("\n📋 PHASE 3: COMPONENT INTEGRATION TESTS")
        print("-" * 50)
        integration_success = self._run_integration_tests()
        
        # Phase 4: Trading System Readiness
        print("\n📋 PHASE 4: TRADING SYSTEM READINESS")
        print("-" * 50)
        trading_success = self._run_trading_readiness_checks()
        
        # Phase 5: Final System Assessment
        print("\n📋 PHASE 5: FINAL SYSTEM ASSESSMENT")
        print("-" * 50)
        final_success = self._run_final_assessment()
        
        # Calculate total validation time
        validation_time = time.time() - validation_start
        
        # Generate comprehensive report
        overall_success = self._generate_startup_report(
            dep_success, config_success, integration_success, 
            trading_success, final_success, validation_time
        )
        
        return overall_success
    
    def _run_dependency_checks(self) -> bool:
        """Run system dependency checks"""
        try:
            print("   🔍 Running system dependency checker...")
            
            # Run the system dependency checker
            result = subprocess.run([
                sys.executable, 'system_dependency_checker.py'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ System dependencies: ALL CHECKS PASSED")
                self.validation_results['dependencies'] = {
                    'status': 'PASS',
                    'message': 'All system dependencies validated'
                }
                return True
            else:
                print("   ⚠️ System dependencies: WARNINGS DETECTED")
                self.validation_results['dependencies'] = {
                    'status': 'WARNING',
                    'message': 'Some dependency warnings detected'
                }
                return True  # Warnings are acceptable for startup
                
        except Exception as e:
            print(f"   ❌ System dependencies: ERROR - {e}")
            self.validation_results['dependencies'] = {
                'status': 'FAIL',
                'message': f'Dependency check failed: {e}'
            }
            return False
    
    def _run_configuration_validation(self) -> bool:
        """Run configuration validation"""
        try:
            print("   🔧 Running configuration validator...")
            
            # Run the enhanced system validator
            result = subprocess.run([
                sys.executable, 'enhanced_system_validator.py'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ Configuration validation: ALL CHECKS PASSED")
                self.validation_results['configuration'] = {
                    'status': 'PASS',
                    'message': 'All configurations validated and fixed'
                }
                return True
            else:
                print("   ❌ Configuration validation: ISSUES DETECTED")
                self.validation_results['configuration'] = {
                    'status': 'FAIL',
                    'message': 'Configuration validation failed'
                }
                return False
                
        except Exception as e:
            print(f"   ❌ Configuration validation: ERROR - {e}")
            self.validation_results['configuration'] = {
                'status': 'FAIL',
                'message': f'Configuration check failed: {e}'
            }
            return False
    
    def _run_integration_tests(self) -> bool:
        """Run component integration tests"""
        try:
            print("   🧪 Testing component integrations...")
            
            # Test critical component imports
            critical_components = [
                ('launch_epinnox', 'Main trading interface'),
                ('symbol_scanner', 'Symbol scanner'),
                ('core.llm_orchestrator', 'LLM orchestrator'),
                ('portfolio.portfolio_manager', 'Portfolio manager'),
                ('core.risk_management_system', 'Risk management'),
                ('trading.ccxt_trading_engine', 'Trading engine')
            ]
            
            integration_failures = []
            
            for component, description in critical_components:
                try:
                    __import__(component)
                    print(f"   ✅ {description}: Import successful")
                except ImportError as e:
                    print(f"   ❌ {description}: Import failed - {e}")
                    integration_failures.append(f"{description}: {e}")
            
            if not integration_failures:
                print("   ✅ Component integration: ALL COMPONENTS AVAILABLE")
                self.validation_results['integration'] = {
                    'status': 'PASS',
                    'message': 'All critical components integrated successfully'
                }
                return True
            else:
                print(f"   ❌ Component integration: {len(integration_failures)} FAILURES")
                self.validation_results['integration'] = {
                    'status': 'FAIL',
                    'message': f'{len(integration_failures)} component integration failures'
                }
                return False
                
        except Exception as e:
            print(f"   ❌ Integration tests: ERROR - {e}")
            self.validation_results['integration'] = {
                'status': 'FAIL',
                'message': f'Integration test failed: {e}'
            }
            return False
    
    def _run_trading_readiness_checks(self) -> bool:
        """Run trading system readiness checks"""
        try:
            print("   💰 Checking trading system readiness...")
            
            # Check for trading-specific requirements
            trading_checks = []
            
            # Check credentials
            cred_path = self.project_root / 'credentials.py'
            if cred_path.exists():
                trading_checks.append("✅ Credentials file exists")
            else:
                trading_checks.append("❌ Credentials file missing")
            
            # Check trading configuration
            config_path = self.project_root / 'config' / 'trading_config.py'
            if config_path.exists():
                trading_checks.append("✅ Trading configuration exists")
            else:
                trading_checks.append("❌ Trading configuration missing")
            
            # Check required directories
            required_dirs = ['logs', 'data', 'cache', 'backups']
            missing_dirs = []
            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    trading_checks.append(f"✅ {dir_name} directory exists")
                else:
                    missing_dirs.append(dir_name)
                    trading_checks.append(f"❌ {dir_name} directory missing")
            
            # Display trading checks
            for check in trading_checks:
                print(f"   {check}")
            
            # Determine trading readiness
            failed_checks = [c for c in trading_checks if c.startswith("❌")]
            
            if not failed_checks:
                print("   ✅ Trading system: FULLY READY")
                self.validation_results['trading'] = {
                    'status': 'PASS',
                    'message': 'Trading system fully ready for deployment'
                }
                return True
            elif len(failed_checks) <= 2:
                print("   ⚠️ Trading system: MOSTLY READY")
                self.validation_results['trading'] = {
                    'status': 'WARNING',
                    'message': f'{len(failed_checks)} minor issues detected'
                }
                return True
            else:
                print("   ❌ Trading system: NOT READY")
                self.validation_results['trading'] = {
                    'status': 'FAIL',
                    'message': f'{len(failed_checks)} critical issues detected'
                }
                return False
                
        except Exception as e:
            print(f"   ❌ Trading readiness: ERROR - {e}")
            self.validation_results['trading'] = {
                'status': 'FAIL',
                'message': f'Trading readiness check failed: {e}'
            }
            return False
    
    def _run_final_assessment(self) -> bool:
        """Run final system assessment"""
        try:
            print("   🎯 Performing final system assessment...")
            
            # Count validation results
            passed = sum(1 for r in self.validation_results.values() if r['status'] == 'PASS')
            warnings = sum(1 for r in self.validation_results.values() if r['status'] == 'WARNING')
            failed = sum(1 for r in self.validation_results.values() if r['status'] == 'FAIL')
            total = len(self.validation_results)
            
            print(f"   📊 Validation Summary: {passed} passed, {warnings} warnings, {failed} failed")
            
            # Calculate system readiness score
            readiness_score = ((passed * 100) + (warnings * 70)) / (total * 100) * 100
            
            print(f"   📈 System Readiness Score: {readiness_score:.1f}%")
            
            # Determine final status
            if failed == 0 and warnings <= 1:
                final_status = "FULLY READY"
                status_icon = "🟢"
                ready = True
            elif failed == 0:
                final_status = "MOSTLY READY"
                status_icon = "🟡"
                ready = True
            else:
                final_status = "NEEDS ATTENTION"
                status_icon = "🔴"
                ready = False
            
            print(f"   {status_icon} Final Assessment: {final_status}")
            
            self.validation_results['final'] = {
                'status': 'PASS' if ready else 'FAIL',
                'message': f'System {final_status.lower()}',
                'readiness_score': readiness_score
            }
            
            return ready
            
        except Exception as e:
            print(f"   ❌ Final assessment: ERROR - {e}")
            self.validation_results['final'] = {
                'status': 'FAIL',
                'message': f'Final assessment failed: {e}'
            }
            return False
    
    def _generate_startup_report(self, dep_success: bool, config_success: bool, 
                               integration_success: bool, trading_success: bool, 
                               final_success: bool, validation_time: float) -> bool:
        """Generate comprehensive startup validation report"""
        
        print(f"\n🎯 COMPREHENSIVE STARTUP VALIDATION REPORT")
        print("=" * 70)
        
        # Overall statistics
        total_phases = 5
        passed_phases = sum([dep_success, config_success, integration_success, trading_success, final_success])
        
        print(f"\n📊 VALIDATION STATISTICS:")
        print(f"   ⏱️ Total Validation Time: {validation_time:.2f}s")
        print(f"   📋 Phases Completed: {total_phases}")
        print(f"   ✅ Phases Passed: {passed_phases}")
        print(f"   📈 Success Rate: {(passed_phases/total_phases)*100:.1f}%")
        
        # Phase results
        print(f"\n📋 PHASE RESULTS:")
        phases = [
            ("System Dependencies", dep_success),
            ("Configuration Validation", config_success),
            ("Component Integration", integration_success),
            ("Trading System Readiness", trading_success),
            ("Final Assessment", final_success)
        ]
        
        for phase_name, success in phases:
            status_icon = "✅" if success else "❌"
            print(f"   {status_icon} {phase_name}: {'PASSED' if success else 'FAILED'}")
        
        # Detailed results
        print(f"\n📋 DETAILED VALIDATION RESULTS:")
        for component, result in self.validation_results.items():
            status_icon = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌"}[result['status']]
            print(f"   {status_icon} {component.title()}: {result['message']}")
        
        # Final recommendation
        overall_success = passed_phases >= 4  # Allow one phase to have warnings
        
        print(f"\n🚀 FINAL STARTUP RECOMMENDATION:")
        if overall_success:
            print("   🟢 SYSTEM READY FOR EPINNOX v6 LAUNCH")
            print("   ✅ All critical validations passed")
            print("   💰 Ready for autonomous trading operations")
            print("   🎯 Proceed with system startup")
        else:
            print("   🔴 SYSTEM NOT READY FOR LAUNCH")
            print("   ❌ Critical validation failures detected")
            print("   🛠️ Address failures before attempting startup")
            print("   📋 Review detailed results above")
        
        # Save validation report
        self._save_validation_report(validation_time, overall_success)
        
        return overall_success
    
    def _save_validation_report(self, validation_time: float, success: bool):
        """Save validation report to file"""
        try:
            report_dir = self.project_root / 'logs' / 'system'
            report_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"startup_validation_{timestamp}.log"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"Epinnox v6 Startup Validation Report\n")
                f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Validation Time: {validation_time:.2f}s\n")
                f.write(f"Overall Success: {success}\n\n")
                
                f.write("Detailed Results:\n")
                for component, result in self.validation_results.items():
                    f.write(f"  {component}: {result['status']} - {result['message']}\n")
            
            print(f"\n📄 Validation report saved: {report_file}")
            
        except Exception as e:
            print(f"⚠️ Could not save validation report: {e}")

def main():
    """Main execution for startup validation"""
    validator = StartupValidationSuite()
    success = validator.run_complete_validation()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
