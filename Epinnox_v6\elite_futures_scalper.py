#!/usr/bin/env python3
"""
Elite Futures Scalper System for Epinnox v6
Aggressive high-frequency scalping AI for cryptocurrency futures trading
"""

import sys
import os
import time
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EliteFuturesScalper:
    """
    Elite Futures Scalper System
    Aggressive high-frequency scalping AI for cryptocurrency futures trading
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logs_dir = self.project_root / 'logs'
        
        # Trading parameters
        self.target_pip_range = (0.02, 0.15)  # 2-15 pip movements (0.02% - 0.15% gains)
        self.leverage_range = (10, 50)        # 10x-50x leverage
        self.hold_duration_range = (30, 300)  # 30 seconds to 5 minutes
        self.position_size_range = (1.0, 3.0) # 1-3% of account balance
        self.min_confidence = 60              # Minimum confidence to trade
        self.action_bias = True               # Strongly favor action over waiting
        
        # Risk management
        self.max_account_risk = 3.0           # Maximum 3% account risk per trade
        self.stop_loss_range = (0.5, 2.0)    # 0.5-2% from entry
        self.take_profit_range = (1.0, 5.0)  # 1-5% from entry
        self.min_risk_reward = 1.5            # Minimum 1:1.5 risk/reward ratio
        
        # Market condition thresholds
        self.max_spread_pct = 0.5             # Maximum 0.5% spread
        self.min_volume_pct = 50              # Minimum 50% of average volume
        
        logger.info("🚀 Elite Futures Scalper initialized")
    
    def analyze_market_and_decide(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market data and make aggressive scalping decision
        Returns JSON decision in required format
        """
        try:
            # Extract market data
            symbol = market_data.get('symbol', 'BTC/USDT:USDT')
            current_price = float(market_data.get('current_price', 0))
            volume = market_data.get('volume', 0)
            spread = market_data.get('spread', 0)
            account_balance = float(market_data.get('account_balance', 1000))
            
            # Get technical indicators
            technical_signals = self._analyze_technical_indicators(market_data)
            
            # Check market conditions
            market_conditions = self._check_market_conditions(market_data)
            
            # Calculate confidence based on signals
            confidence = self._calculate_confidence(technical_signals, market_conditions)
            
            # Make trading decision
            decision = self._make_scalping_decision(
                confidence, technical_signals, market_conditions, 
                current_price, account_balance
            )
            
            # Log decision
            self._log_scalping_decision(decision, market_data)
            
            return decision
            
        except Exception as e:
            logger.error(f"Error in market analysis: {e}")
            return self._get_safe_wait_decision(str(e))
    
    def _analyze_technical_indicators(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze technical indicators for scalping signals"""
        try:
            # Extract price data
            current_price = float(market_data.get('current_price', 0))
            recent_prices = market_data.get('recent_prices', [current_price])
            
            if len(recent_prices) < 2:
                recent_prices = [current_price] * 10
            
            # Calculate short-term momentum
            price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] * 100
            
            # Calculate volatility
            if len(recent_prices) > 1:
                volatility = self._calculate_volatility(recent_prices)
            else:
                volatility = 1.0
            
            # Determine trend direction
            if price_change > 0.05:  # 0.05% upward movement
                trend_direction = "BULLISH"
                trend_strength = min(abs(price_change) * 10, 100)
            elif price_change < -0.05:  # 0.05% downward movement
                trend_direction = "BEARISH"
                trend_strength = min(abs(price_change) * 10, 100)
            else:
                trend_direction = "SIDEWAYS"
                trend_strength = 30
            
            # Check for breakout patterns
            breakout_signal = self._detect_breakout(recent_prices)
            
            return {
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'price_change': price_change,
                'volatility': volatility,
                'breakout_signal': breakout_signal,
                'momentum_score': min(abs(price_change) * 20, 100)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing technical indicators: {e}")
            return {
                'trend_direction': 'SIDEWAYS',
                'trend_strength': 30,
                'price_change': 0,
                'volatility': 1.0,
                'breakout_signal': False,
                'momentum_score': 30
            }
    
    def _calculate_volatility(self, prices: List[float]) -> float:
        """Calculate price volatility"""
        if len(prices) < 2:
            return 1.0
        
        returns = []
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        if not returns:
            return 1.0
        
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        volatility = (variance ** 0.5) * 100  # Convert to percentage
        
        return max(volatility, 0.1)
    
    def _detect_breakout(self, prices: List[float]) -> bool:
        """Detect breakout patterns for scalping"""
        if len(prices) < 5:
            return False
        
        # Check for recent price acceleration
        recent_change = abs(prices[-1] - prices[-3]) / prices[-3] * 100
        
        # Breakout if recent change > 0.1%
        return recent_change > 0.1
    
    def _check_market_conditions(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check market conditions for scalping suitability"""
        try:
            current_price = float(market_data.get('current_price', 0))
            volume = market_data.get('volume', 1000000)
            spread = market_data.get('spread', 0)
            avg_volume = market_data.get('avg_volume', volume)
            
            # Calculate spread percentage
            spread_pct = (spread / current_price * 100) if current_price > 0 else 0
            
            # Calculate volume ratio
            volume_ratio = (volume / avg_volume * 100) if avg_volume > 0 else 100
            
            # Check for news events (placeholder)
            news_pending = market_data.get('news_pending', False)
            
            # Overall market health score
            health_score = 100
            
            if spread_pct > self.max_spread_pct:
                health_score -= 30
            
            if volume_ratio < self.min_volume_pct:
                health_score -= 25
            
            if news_pending:
                health_score -= 20
            
            return {
                'spread_pct': spread_pct,
                'volume_ratio': volume_ratio,
                'news_pending': news_pending,
                'health_score': max(health_score, 0),
                'suitable_for_scalping': health_score >= 60
            }
            
        except Exception as e:
            logger.error(f"Error checking market conditions: {e}")
            return {
                'spread_pct': 1.0,
                'volume_ratio': 50,
                'news_pending': True,
                'health_score': 30,
                'suitable_for_scalping': False
            }
    
    def _calculate_confidence(self, technical_signals: Dict[str, Any], 
                            market_conditions: Dict[str, Any]) -> int:
        """Calculate trading confidence score"""
        try:
            base_confidence = 50
            
            # Add confidence based on trend strength
            trend_strength = technical_signals.get('trend_strength', 30)
            base_confidence += min(trend_strength * 0.3, 25)
            
            # Add confidence based on momentum
            momentum_score = technical_signals.get('momentum_score', 30)
            base_confidence += min(momentum_score * 0.2, 15)
            
            # Add confidence for breakout signals
            if technical_signals.get('breakout_signal', False):
                base_confidence += 10
            
            # Add confidence based on market health
            health_score = market_conditions.get('health_score', 50)
            base_confidence += min(health_score * 0.1, 10)
            
            # Reduce confidence for poor market conditions
            if not market_conditions.get('suitable_for_scalping', True):
                base_confidence -= 20
            
            # Ensure confidence is within valid range
            confidence = max(min(int(base_confidence), 100), 0)
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 50
    
    def _make_scalping_decision(self, confidence: int, technical_signals: Dict[str, Any],
                              market_conditions: Dict[str, Any], current_price: float,
                              account_balance: float) -> Dict[str, Any]:
        """Make aggressive scalping decision based on analysis"""
        try:
            # Check if market conditions are severely broken
            if (market_conditions.get('spread_pct', 0) > self.max_spread_pct or
                market_conditions.get('volume_ratio', 100) < self.min_volume_pct or
                market_conditions.get('news_pending', False)):
                
                if confidence < 65:  # Only wait if confidence is also low
                    return self._get_safe_wait_decision("Poor market conditions")
            
            # Determine action based on trend and confidence
            trend_direction = technical_signals.get('trend_direction', 'SIDEWAYS')
            
            if confidence >= 70:
                # High confidence - execute trade immediately
                if trend_direction == 'BULLISH':
                    action = 'LONG'
                elif trend_direction == 'BEARISH':
                    action = 'SHORT'
                else:
                    # Even sideways markets can be scalped with high confidence
                    action = 'LONG' if technical_signals.get('price_change', 0) >= 0 else 'SHORT'
            
            elif confidence >= 60:
                # Medium confidence - execute with reduced position size
                if trend_direction in ['BULLISH', 'BEARISH']:
                    action = 'LONG' if trend_direction == 'BULLISH' else 'SHORT'
                else:
                    # Favor action over waiting (action bias)
                    action = 'LONG' if technical_signals.get('momentum_score', 50) > 50 else 'SHORT'
            
            else:
                # Low confidence - only wait if market is clearly broken
                if self.action_bias and market_conditions.get('suitable_for_scalping', True):
                    # Still take action due to action bias
                    action = 'LONG' if technical_signals.get('price_change', 0) >= 0 else 'SHORT'
                    confidence = 60  # Boost confidence due to action bias
                else:
                    return self._get_safe_wait_decision("Low confidence and poor conditions")
            
            # Calculate position parameters
            position_params = self._calculate_position_parameters(
                action, confidence, current_price, account_balance, technical_signals
            )
            
            # Create decision JSON
            decision = {
                'action': action,
                'quantity': position_params['quantity'],
                'leverage': position_params['leverage'],
                'stop_loss': position_params['stop_loss'],
                'take_profit': position_params['take_profit'],
                'risk_pct': position_params['risk_pct'],
                'order_type': position_params['order_type'],
                'confidence': confidence,
                'reasoning': self._generate_reasoning(action, technical_signals, confidence)
            }
            
            return decision
            
        except Exception as e:
            logger.error(f"Error making scalping decision: {e}")
            return self._get_safe_wait_decision(f"Decision error: {e}")
    
    def _calculate_position_parameters(self, action: str, confidence: int, 
                                     current_price: float, account_balance: float,
                                     technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position sizing and risk parameters"""
        try:
            # Calculate risk percentage based on confidence
            if confidence >= 80:
                risk_pct = 2.5
            elif confidence >= 70:
                risk_pct = 2.0
            else:
                risk_pct = 1.5
            
            # Adjust for volatility
            volatility = technical_signals.get('volatility', 1.0)
            if volatility > 2.0:
                risk_pct *= 0.8  # Reduce risk in high volatility
            
            # Calculate leverage based on confidence and volatility
            if confidence >= 80 and volatility < 1.5:
                leverage = 30
            elif confidence >= 70:
                leverage = 20
            else:
                leverage = 15
            
            # Calculate position size
            risk_amount = account_balance * (risk_pct / 100)
            
            # Calculate stop loss and take profit
            if volatility > 2.0:
                stop_loss = 1.5  # Wider stops in high volatility
                take_profit = 3.0
            else:
                stop_loss = 1.0  # Tight stops for scalping
                take_profit = 2.0
            
            # Ensure minimum risk/reward ratio
            if take_profit / stop_loss < self.min_risk_reward:
                take_profit = stop_loss * self.min_risk_reward
            
            # Calculate quantity
            stop_loss_amount = current_price * (stop_loss / 100)
            quantity = risk_amount / stop_loss_amount
            
            # Order type - use market orders for aggressive scalping
            order_type = 'MARKET'
            
            return {
                'quantity': round(quantity, 6),
                'leverage': leverage,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_pct': risk_pct,
                'order_type': order_type
            }
            
        except Exception as e:
            logger.error(f"Error calculating position parameters: {e}")
            return {
                'quantity': 0.001,
                'leverage': 10,
                'stop_loss': 1.0,
                'take_profit': 2.0,
                'risk_pct': 1.0,
                'order_type': 'MARKET'
            }
    
    def _generate_reasoning(self, action: str, technical_signals: Dict[str, Any], 
                          confidence: int) -> str:
        """Generate brief reasoning for the decision (max 50 words)"""
        try:
            trend = technical_signals.get('trend_direction', 'SIDEWAYS')
            momentum = technical_signals.get('momentum_score', 50)
            breakout = technical_signals.get('breakout_signal', False)
            
            if breakout:
                return f"{action} on breakout signal, {trend} trend, {confidence}% confidence"
            elif momentum > 70:
                return f"{action} on strong momentum ({momentum}), {trend} trend"
            elif confidence >= 80:
                return f"High confidence {action} ({confidence}%), favorable {trend} conditions"
            else:
                return f"{action} scalp opportunity, {trend} bias, action-favored entry"
                
        except Exception as e:
            return f"{action} trade based on technical analysis"
    
    def _get_safe_wait_decision(self, reason: str) -> Dict[str, Any]:
        """Return safe WAIT decision"""
        return {
            'action': 'WAIT',
            'quantity': 0,
            'leverage': 1,
            'stop_loss': 0,
            'take_profit': 0,
            'risk_pct': 0,
            'order_type': 'MARKET',
            'confidence': 0,
            'reasoning': f"WAIT: {reason[:40]}"
        }
    
    def _log_scalping_decision(self, decision: Dict[str, Any], market_data: Dict[str, Any]):
        """Log scalping decision for analysis"""
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'symbol': market_data.get('symbol', 'UNKNOWN'),
                'price': market_data.get('current_price', 0),
                'decision': decision,
                'market_data_summary': {
                    'volume': market_data.get('volume', 0),
                    'spread': market_data.get('spread', 0)
                }
            }
            
            # Log to scalping decisions file
            log_file = self.logs_dir / 'scalping_decisions.log'
            log_file.parent.mkdir(exist_ok=True)
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{json.dumps(log_entry)}\n")
                
        except Exception as e:
            logger.error(f"Error logging scalping decision: {e}")

def create_scalper_prompt_integration():
    """Create integration with existing Epinnox system"""
    integration_code = '''
# Elite Futures Scalper Integration for Epinnox v6
# Add this to your LLM orchestrator or trading interface

from elite_futures_scalper import EliteFuturesScalper

class ScalperIntegration:
    def __init__(self):
        self.scalper = EliteFuturesScalper()
    
    def get_scalping_decision(self, market_data):
        """Get aggressive scalping decision from Elite Futures Scalper"""
        return self.scalper.analyze_market_and_decide(market_data)
    
    def format_for_llm(self, market_data):
        """Format market data for LLM with scalper prompt"""
        decision = self.get_scalping_decision(market_data)
        
        prompt = f"""
You are an aggressive high-frequency scalping AI for cryptocurrency futures trading.
Your primary directive is to execute rapid, profitable trades with minimal hesitation.

Current Market Analysis:
- Symbol: {market_data.get('symbol', 'BTC/USDT:USDT')}
- Price: ${market_data.get('current_price', 0)}
- Volume: {market_data.get('volume', 0)}
- Account Balance: ${market_data.get('account_balance', 1000)}

Elite Scalper Recommendation: {json.dumps(decision, indent=2)}

Respond with the exact JSON format only. No additional commentary.
"""
        return prompt

# Usage in existing system:
# scalper_integration = ScalperIntegration()
# decision = scalper_integration.get_scalping_decision(market_data)
'''
    
    # Save integration code
    integration_file = Path('scalper_integration.py')
    with open(integration_file, 'w', encoding='utf-8') as f:
        f.write(integration_code)
    
    print(f"✅ Scalper integration created: {integration_file}")

def main():
    """Main execution for Elite Futures Scalper"""
    scalper = EliteFuturesScalper()
    
    # Test with sample market data
    sample_market_data = {
        'symbol': 'BTC/USDT:USDT',
        'current_price': 50000.0,
        'volume': 1000000,
        'spread': 10.0,
        'avg_volume': 1200000,
        'account_balance': 1000.0,
        'recent_prices': [49950, 49980, 50020, 50000],
        'news_pending': False
    }
    
    # Get scalping decision
    decision = scalper.analyze_market_and_decide(sample_market_data)
    
    print("🚀 ELITE FUTURES SCALPER DECISION:")
    print(json.dumps(decision, indent=2))
    
    # Create integration
    create_scalper_prompt_integration()
    
    return decision

if __name__ == '__main__':
    main()
