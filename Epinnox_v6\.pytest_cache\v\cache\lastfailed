{"tests/test_gui_components.py": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_position_display_integration": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_portfolio_exposure_warning_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_llm_orchestrator_results_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_leverage_mismatch_error_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_emergency_stop_accessibility": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_real_time_data_integration": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_account_balance_display": true, "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_websocket_disconnection_handling": true, "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_portfolio_overexposure_prevention": true, "test_gui_comprehensive_enhanced.py::TestEnhancedMainWindowComponents::test_enhanced_main_window_initialization": true, "test_gui_comprehensive_enhanced.py::TestEnhancedMainWindowComponents::test_main_window_component_interactions": true, "test_gui_comprehensive_enhanced.py::TestEnhancedMainWindowComponents::test_main_window_screenshot_capture": true, "test_gui_comprehensive_enhanced.py::TestEnhancedAutonomousIntegration::test_enhanced_autonomous_workflow": true, "test_gui_comprehensive_enhanced.py::TestEnhancedAutonomousIntegration::test_integration_error_handling": true, "test_gui_comprehensive_enhanced.py::test_enhanced_pytest_qt_integration": true, "test_gui_comprehensive_enhanced.py::TestEnhancedEndToEndWorkflows::test_complete_trading_workflow": true, "test_gui_comprehensive_enhanced.py::TestEnhancedEndToEndWorkflows::test_error_recovery_workflow": true}