#!/usr/bin/env python3
"""
Comprehensive System Dependency & Configuration Checker
Validates all system requirements, dependencies, and configurations before launching Epinnox
"""

import sys
import os
import time
import psutil
import platform
import subprocess
import logging
import json
import yaml
import requests
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SystemRequirements:
    """System requirements specification"""
    min_python_version: Tuple[int, int, int] = (3, 8, 0)
    min_ram_gb: float = 4.0
    min_cpu_cores: int = 2
    min_disk_space_gb: float = 1.0
    required_packages: Dict[str, str] = None
    
    def __post_init__(self):
        if self.required_packages is None:
            self.required_packages = {
                'PyQt5': '5.15.0',
                'ccxt': '4.0.0',
                'pandas': '1.3.0',
                'numpy': '1.21.0',
                'requests': '2.25.0',
                'pyyaml': '5.4.0',
                'psutil': '5.8.0',
                'asyncio': '3.4.3',
                'websockets': '10.0',
                'aiohttp': '3.8.0'
            }

@dataclass
class CheckResult:
    """Result of a system check"""
    name: str
    status: str  # 'PASS', 'FAIL', 'WARNING'
    message: str
    details: Optional[Dict] = None
    fix_suggestion: Optional[str] = None

class ComprehensiveSystemChecker:
    """Comprehensive system dependency and configuration checker"""
    
    def __init__(self):
        self.requirements = SystemRequirements()
        self.check_results: List[CheckResult] = []
        self.startup_time = time.time()
        self.project_root = Path(__file__).parent
        
        # Required directories
        self.required_dirs = [
            'logs', 'cache', 'data', 'config', 'screenshots', 
            'test_results', 'backups', 'temp', 'exports'
        ]
        
        # Required configuration files
        self.required_configs = [
            'config/config.yaml',
            'config/trading_config.py',
            'credentials.py'
        ]
        
        logger.info("🔍 Comprehensive System Checker initialized")
    
    def run_all_checks(self) -> bool:
        """Run all system checks and return overall status"""
        print("🔍 COMPREHENSIVE SYSTEM DEPENDENCY & CONFIGURATION CHECKS")
        print("=" * 70)
        
        start_time = time.time()
        
        # Run all check categories
        self._check_python_version()
        self._check_system_resources()
        self._check_network_connectivity()
        self._check_package_dependencies()
        self._check_directory_structure()
        self._check_configuration_files()
        self._check_api_credentials()
        self._check_file_permissions()
        self._check_running_instances()
        self._check_system_performance()
        
        # Calculate startup time
        total_time = time.time() - start_time
        
        # Generate comprehensive report
        success = self._generate_system_report(total_time)
        
        return success
    
    def _check_python_version(self):
        """Check Python version compatibility"""
        try:
            current_version = sys.version_info[:3]
            min_version = self.requirements.min_python_version
            
            if current_version >= min_version:
                self.check_results.append(CheckResult(
                    name="Python Version",
                    status="PASS",
                    message=f"Python {'.'.join(map(str, current_version))} meets minimum requirement",
                    details={'current': current_version, 'required': min_version}
                ))
            else:
                self.check_results.append(CheckResult(
                    name="Python Version",
                    status="FAIL",
                    message=f"Python {'.'.join(map(str, current_version))} below minimum {'.'.join(map(str, min_version))}",
                    details={'current': current_version, 'required': min_version},
                    fix_suggestion="Upgrade Python to version 3.8 or higher"
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                name="Python Version",
                status="FAIL",
                message=f"Error checking Python version: {e}",
                fix_suggestion="Verify Python installation"
            ))
    
    def _check_system_resources(self):
        """Check system resources (RAM, CPU, disk space)"""
        try:
            # Check RAM
            ram_gb = psutil.virtual_memory().total / (1024**3)
            if ram_gb >= self.requirements.min_ram_gb:
                ram_status = "PASS"
                ram_message = f"RAM: {ram_gb:.1f}GB available (≥{self.requirements.min_ram_gb}GB required)"
            else:
                ram_status = "WARNING"
                ram_message = f"RAM: {ram_gb:.1f}GB available (<{self.requirements.min_ram_gb}GB recommended)"
            
            self.check_results.append(CheckResult(
                name="System RAM",
                status=ram_status,
                message=ram_message,
                details={'available_gb': ram_gb, 'required_gb': self.requirements.min_ram_gb}
            ))
            
            # Check CPU cores
            cpu_cores = psutil.cpu_count(logical=False)
            if cpu_cores >= self.requirements.min_cpu_cores:
                cpu_status = "PASS"
                cpu_message = f"CPU: {cpu_cores} cores available (≥{self.requirements.min_cpu_cores} required)"
            else:
                cpu_status = "WARNING"
                cpu_message = f"CPU: {cpu_cores} cores available (<{self.requirements.min_cpu_cores} recommended)"
            
            self.check_results.append(CheckResult(
                name="CPU Cores",
                status=cpu_status,
                message=cpu_message,
                details={'available': cpu_cores, 'required': self.requirements.min_cpu_cores}
            ))
            
            # Check disk space
            disk_usage = psutil.disk_usage(str(self.project_root))
            free_gb = disk_usage.free / (1024**3)
            
            if free_gb >= self.requirements.min_disk_space_gb:
                disk_status = "PASS"
                disk_message = f"Disk Space: {free_gb:.1f}GB free (≥{self.requirements.min_disk_space_gb}GB required)"
            else:
                disk_status = "FAIL"
                disk_message = f"Disk Space: {free_gb:.1f}GB free (<{self.requirements.min_disk_space_gb}GB required)"
            
            self.check_results.append(CheckResult(
                name="Disk Space",
                status=disk_status,
                message=disk_message,
                details={'free_gb': free_gb, 'required_gb': self.requirements.min_disk_space_gb},
                fix_suggestion="Free up disk space or move to a drive with more space" if disk_status == "FAIL" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="System Resources",
                status="FAIL",
                message=f"Error checking system resources: {e}",
                fix_suggestion="Verify system monitoring tools are available"
            ))
    
    def _check_network_connectivity(self):
        """Check network connectivity for trading operations"""
        try:
            # Test basic internet connectivity
            response = requests.get('https://httpbin.org/status/200', timeout=10)
            if response.status_code == 200:
                internet_status = "PASS"
                internet_message = "Internet connectivity: Available"
            else:
                internet_status = "FAIL"
                internet_message = f"Internet connectivity: Failed (Status: {response.status_code})"
            
            self.check_results.append(CheckResult(
                name="Internet Connectivity",
                status=internet_status,
                message=internet_message,
                fix_suggestion="Check internet connection and firewall settings" if internet_status == "FAIL" else None
            ))
            
            # Test trading exchange connectivity
            exchange_endpoints = [
                ('HTX API', 'https://api.hbdm.com/heartbeat'),
                ('Binance API', 'https://api.binance.com/api/v3/ping'),
                ('OKX API', 'https://www.okx.com/api/v5/public/time')
            ]
            
            for name, url in exchange_endpoints:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        status = "PASS"
                        message = f"{name}: Accessible"
                    else:
                        status = "WARNING"
                        message = f"{name}: Status {response.status_code}"
                except Exception as e:
                    status = "WARNING"
                    message = f"{name}: Connection failed ({str(e)[:50]})"
                
                self.check_results.append(CheckResult(
                    name=f"Exchange API - {name}",
                    status=status,
                    message=message
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                name="Network Connectivity",
                status="FAIL",
                message=f"Error checking network connectivity: {e}",
                fix_suggestion="Verify internet connection and DNS settings"
            ))
    
    def _check_package_dependencies(self):
        """Check Python package dependencies and versions"""
        try:
            import pkg_resources
            
            for package_name, min_version in self.requirements.required_packages.items():
                try:
                    # Try to get installed version
                    installed_version = pkg_resources.get_distribution(package_name).version
                    
                    # Compare versions (simplified comparison)
                    if self._compare_versions(installed_version, min_version) >= 0:
                        status = "PASS"
                        message = f"{package_name}: {installed_version} (≥{min_version} required)"
                    else:
                        status = "FAIL"
                        message = f"{package_name}: {installed_version} (<{min_version} required)"
                        
                except pkg_resources.DistributionNotFound:
                    status = "FAIL"
                    message = f"{package_name}: Not installed (≥{min_version} required)"
                    
                except Exception as e:
                    status = "WARNING"
                    message = f"{package_name}: Version check failed ({e})"
                
                self.check_results.append(CheckResult(
                    name=f"Package - {package_name}",
                    status=status,
                    message=message,
                    fix_suggestion=f"Install/upgrade: pip install {package_name}>={min_version}" if status == "FAIL" else None
                ))
                
        except Exception as e:
            self.check_results.append(CheckResult(
                name="Package Dependencies",
                status="FAIL",
                message=f"Error checking package dependencies: {e}",
                fix_suggestion="Verify pip and package management tools are available"
            ))
    
    def _compare_versions(self, version1: str, version2: str) -> int:
        """Compare two version strings (simplified)"""
        try:
            v1_parts = [int(x) for x in version1.split('.')]
            v2_parts = [int(x) for x in version2.split('.')]
            
            # Pad shorter version with zeros
            max_len = max(len(v1_parts), len(v2_parts))
            v1_parts.extend([0] * (max_len - len(v1_parts)))
            v2_parts.extend([0] * (max_len - len(v2_parts)))
            
            for v1, v2 in zip(v1_parts, v2_parts):
                if v1 > v2:
                    return 1
                elif v1 < v2:
                    return -1
            return 0
        except:
            return 0  # If comparison fails, assume equal
    
    def _check_directory_structure(self):
        """Check and create required directory structure"""
        try:
            missing_dirs = []
            created_dirs = []
            
            for dir_name in self.required_dirs:
                dir_path = self.project_root / dir_name
                
                if not dir_path.exists():
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        created_dirs.append(dir_name)
                    except Exception as e:
                        missing_dirs.append((dir_name, str(e)))
            
            if not missing_dirs:
                status = "PASS"
                message = f"Directory structure: Complete"
                if created_dirs:
                    message += f" (Created: {', '.join(created_dirs)})"
            else:
                status = "FAIL"
                message = f"Directory structure: {len(missing_dirs)} directories failed to create"
            
            self.check_results.append(CheckResult(
                name="Directory Structure",
                status=status,
                message=message,
                details={'created': created_dirs, 'failed': missing_dirs},
                fix_suggestion="Check directory permissions and disk space" if status == "FAIL" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="Directory Structure",
                status="FAIL",
                message=f"Error checking directory structure: {e}",
                fix_suggestion="Verify file system permissions"
            ))
    
    def _check_configuration_files(self):
        """Check configuration files and validate structure with encoding handling"""
        try:
            for config_file in self.required_configs:
                config_path = self.project_root / config_file

                if config_path.exists():
                    # Try to validate file content based on extension
                    if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                        status, message = self._validate_yaml_with_encoding(config_path, config_file)
                    elif config_file.endswith('.py'):
                        status, message = self._validate_python_with_encoding(config_path, config_file)
                    else:
                        status = "PASS"
                        message = f"{config_file}: File exists"
                else:
                    status = "WARNING"
                    message = f"{config_file}: File not found"

                self.check_results.append(CheckResult(
                    name=f"Config - {config_file}",
                    status=status,
                    message=message,
                    fix_suggestion=f"Create or fix {config_file}" if status in ["FAIL", "WARNING"] else None
                ))

        except Exception as e:
            self.check_results.append(CheckResult(
                name="Configuration Files",
                status="FAIL",
                message=f"Error checking configuration files: {e}",
                fix_suggestion="Verify configuration file accessibility"
            ))

    def _validate_yaml_with_encoding(self, config_path, config_file):
        """Validate YAML file with multiple encoding attempts"""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'ascii']

        for encoding in encodings:
            try:
                with open(config_path, 'r', encoding=encoding) as f:
                    yaml.safe_load(f)
                return "PASS", f"{config_file}: Valid YAML structure (encoding: {encoding})"
            except UnicodeDecodeError:
                continue
            except yaml.YAMLError as e:
                return "FAIL", f"{config_file}: Invalid YAML ({str(e)[:50]})"
            except Exception:
                continue

        return "FAIL", f"{config_file}: Could not decode with any encoding"

    def _validate_python_with_encoding(self, config_path, config_file):
        """Validate Python file with multiple encoding attempts"""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'ascii']

        for encoding in encodings:
            try:
                with open(config_path, 'r', encoding=encoding) as f:
                    content = f.read()
                compile(content, str(config_path), 'exec')
                return "PASS", f"{config_file}: Valid Python syntax (encoding: {encoding})"
            except UnicodeDecodeError:
                continue
            except SyntaxError as e:
                return "FAIL", f"{config_file}: Syntax error ({str(e)[:50]})"
            except Exception:
                continue

        return "FAIL", f"{config_file}: Could not decode with any encoding"
    
    def _check_api_credentials(self):
        """Check API credentials without exposing sensitive data"""
        try:
            credentials_found = False
            credentials_valid = False
            
            # Check if credentials.py exists and has required structure
            cred_path = self.project_root / 'credentials.py'
            if cred_path.exists():
                try:
                    # Import credentials module
                    import importlib.util
                    spec = importlib.util.spec_from_file_location("credentials", cred_path)
                    credentials = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(credentials)
                    
                    # Check for required credential structures
                    required_exchanges = ['htx', 'binance', 'okx']
                    found_exchanges = []
                    
                    for exchange in required_exchanges:
                        if hasattr(credentials, exchange):
                            exchange_creds = getattr(credentials, exchange)
                            if isinstance(exchange_creds, dict):
                                if 'api_key' in exchange_creds and 'secret' in exchange_creds:
                                    # Validate API key format (without exposing)
                                    api_key = exchange_creds['api_key']
                                    if api_key and len(api_key) > 10:
                                        found_exchanges.append(exchange)
                    
                    if found_exchanges:
                        credentials_found = True
                        credentials_valid = True
                        status = "PASS"
                        message = f"API Credentials: Found for {', '.join(found_exchanges)}"
                    else:
                        credentials_found = True
                        status = "WARNING"
                        message = "API Credentials: File exists but no valid credentials found"
                        
                except Exception as e:
                    credentials_found = True
                    status = "FAIL"
                    message = f"API Credentials: Error loading ({str(e)[:50]})"
            else:
                status = "WARNING"
                message = "API Credentials: credentials.py not found"
            
            self.check_results.append(CheckResult(
                name="API Credentials",
                status=status,
                message=message,
                fix_suggestion="Create credentials.py with valid API keys" if status != "PASS" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="API Credentials",
                status="FAIL",
                message=f"Error checking API credentials: {e}",
                fix_suggestion="Verify credentials.py file structure"
            ))
    
    def _check_file_permissions(self):
        """Check file permissions for critical files and directories"""
        try:
            permission_issues = []
            
            # Check write permissions for critical directories
            critical_dirs = ['logs', 'cache', 'data', 'backups', 'temp']
            
            for dir_name in critical_dirs:
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    if not os.access(dir_path, os.W_OK):
                        permission_issues.append(f"{dir_name}: No write permission")
            
            # Check read permissions for config files
            for config_file in self.required_configs:
                config_path = self.project_root / config_file
                if config_path.exists():
                    if not os.access(config_path, os.R_OK):
                        permission_issues.append(f"{config_file}: No read permission")
            
            if not permission_issues:
                status = "PASS"
                message = "File Permissions: All critical files/directories accessible"
            else:
                status = "FAIL"
                message = f"File Permissions: {len(permission_issues)} issues found"
            
            self.check_results.append(CheckResult(
                name="File Permissions",
                status=status,
                message=message,
                details={'issues': permission_issues},
                fix_suggestion="Fix file/directory permissions" if status == "FAIL" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="File Permissions",
                status="FAIL",
                message=f"Error checking file permissions: {e}",
                fix_suggestion="Verify file system access"
            ))
    
    def _check_running_instances(self):
        """Check for running instances to prevent conflicts"""
        try:
            current_pid = os.getpid()
            python_processes = []
            epinnox_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        if proc.info['cmdline']:
                            cmdline = ' '.join(proc.info['cmdline'])
                            if 'epinnox' in cmdline.lower() or 'launch_epinnox' in cmdline.lower():
                                if proc.info['pid'] != current_pid:
                                    epinnox_processes.append(proc.info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if not epinnox_processes:
                status = "PASS"
                message = "Running Instances: No conflicting Epinnox processes found"
            else:
                status = "WARNING"
                message = f"Running Instances: {len(epinnox_processes)} Epinnox processes detected"
            
            self.check_results.append(CheckResult(
                name="Running Instances",
                status=status,
                message=message,
                details={'conflicting_pids': epinnox_processes},
                fix_suggestion="Stop other Epinnox instances before launching" if status == "WARNING" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="Running Instances",
                status="WARNING",
                message=f"Error checking running instances: {e}",
                fix_suggestion="Manually verify no other instances are running"
            ))
    
    def _check_system_performance(self):
        """Check system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            
            performance_issues = []
            
            if cpu_percent > 80:
                performance_issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory_percent > 85:
                performance_issues.append(f"High memory usage: {memory_percent:.1f}%")
            
            if not performance_issues:
                status = "PASS"
                message = f"System Performance: CPU {cpu_percent:.1f}%, RAM {memory_percent:.1f}%"
            else:
                status = "WARNING"
                message = f"System Performance: {', '.join(performance_issues)}"
            
            self.check_results.append(CheckResult(
                name="System Performance",
                status=status,
                message=message,
                details={
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'memory_available_gb': memory.available / (1024**3)
                },
                fix_suggestion="Close unnecessary applications to free resources" if status == "WARNING" else None
            ))
            
        except Exception as e:
            self.check_results.append(CheckResult(
                name="System Performance",
                status="WARNING",
                message=f"Error checking system performance: {e}",
                fix_suggestion="Verify system monitoring capabilities"
            ))
    
    def _generate_system_report(self, check_duration: float) -> bool:
        """Generate comprehensive system check report"""
        print(f"\n📊 COMPREHENSIVE SYSTEM CHECK REPORT")
        print("=" * 70)
        
        # Count results by status
        passed = sum(1 for r in self.check_results if r.status == "PASS")
        failed = sum(1 for r in self.check_results if r.status == "FAIL")
        warnings = sum(1 for r in self.check_results if r.status == "WARNING")
        total = len(self.check_results)
        
        print(f"\n🎯 SUMMARY STATISTICS:")
        print(f"   ✅ Passed: {passed}/{total}")
        print(f"   ❌ Failed: {failed}/{total}")
        print(f"   ⚠️ Warnings: {warnings}/{total}")
        print(f"   ⏱️ Check Duration: {check_duration:.2f}s")
        
        # System information
        print(f"\n💻 SYSTEM INFORMATION:")
        print(f"   🖥️ Platform: {platform.system()} {platform.release()}")
        print(f"   🐍 Python: {platform.python_version()}")
        print(f"   💾 RAM: {psutil.virtual_memory().total / (1024**3):.1f}GB")
        print(f"   🔧 CPU: {psutil.cpu_count()} cores")
        
        # Detailed results
        print(f"\n📋 DETAILED CHECK RESULTS:")
        for result in self.check_results:
            status_icon = {"PASS": "✅", "FAIL": "❌", "WARNING": "⚠️"}[result.status]
            print(f"   {status_icon} {result.name}: {result.message}")
            
            if result.fix_suggestion:
                print(f"      💡 Fix: {result.fix_suggestion}")
        
        # Critical failures
        critical_failures = [r for r in self.check_results if r.status == "FAIL"]
        if critical_failures:
            print(f"\n🚨 CRITICAL ISSUES REQUIRING ATTENTION:")
            for failure in critical_failures:
                print(f"   ❌ {failure.name}: {failure.message}")
                if failure.fix_suggestion:
                    print(f"      💡 {failure.fix_suggestion}")
        
        # Overall status
        if failed == 0:
            if warnings == 0:
                overall_status = "🟢 SYSTEM FULLY READY"
                print(f"\n🎉 {overall_status}")
                print("   ✅ All checks passed - System ready for Epinnox deployment!")
            else:
                overall_status = "🟡 SYSTEM MOSTLY READY"
                print(f"\n⚠️ {overall_status}")
                print("   🔧 Some warnings detected - System functional but optimization recommended")
        else:
            overall_status = "🔴 SYSTEM NEEDS ATTENTION"
            print(f"\n❌ {overall_status}")
            print("   🛠️ Critical issues detected - Address failures before launching Epinnox")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if failed == 0 and warnings == 0:
            print("   🚀 System is fully optimized for Epinnox trading operations")
            print("   💰 Ready for live trading deployment")
        elif failed == 0:
            print("   🔧 Address warnings for optimal performance")
            print("   ✅ System functional for trading operations")
        else:
            print("   🛠️ Fix critical failures before launching")
            print("   📋 Follow fix suggestions above")
        
        return failed == 0

def main():
    """Main execution for system checks"""
    checker = ComprehensiveSystemChecker()
    success = checker.run_all_checks()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
