#!/usr/bin/env python3
"""
Complete System Check for Epinnox v6
Final comprehensive validation before system deployment
"""

import sys
import os
import time
import logging
import psutil
import platform
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteSystemCheck:
    """Complete system check for Epinnox v6 deployment readiness"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.check_results = {}
        self.startup_time = time.time()
        
        logger.info("🎯 Complete System Check initialized")
    
    def run_complete_system_check(self) -> bool:
        """Run complete system check for deployment readiness"""
        print("🎯 EPINNOX v6 COMPLETE SYSTEM CHECK")
        print("=" * 60)
        print("Final validation before deployment")
        
        check_start = time.time()
        
        # Core system checks
        checks = [
            ("System Requirements", self._check_system_requirements),
            ("Python Environment", self._check_python_environment),
            ("Essential Packages", self._check_essential_packages),
            ("Directory Structure", self._check_directory_structure),
            ("Configuration Files", self._check_configuration_files),
            ("Network Connectivity", self._check_network_connectivity),
            ("Component Integration", self._check_component_integration),
            ("Trading System", self._check_trading_system),
            ("Performance", self._check_performance),
            ("Security", self._check_security)
        ]
        
        passed = 0
        total = len(checks)
        
        for check_name, check_func in checks:
            print(f"\n🔍 {check_name}...")
            try:
                result = check_func()
                if result:
                    print(f"   ✅ {check_name}: PASSED")
                    passed += 1
                else:
                    print(f"   ❌ {check_name}: FAILED")
            except Exception as e:
                print(f"   ❌ {check_name}: ERROR - {e}")
        
        check_time = time.time() - check_start
        
        # Generate final report
        success = self._generate_final_report(passed, total, check_time)
        
        return success
    
    def _check_system_requirements(self) -> bool:
        """Check basic system requirements"""
        try:
            # RAM check
            ram_gb = psutil.virtual_memory().total / (1024**3)
            ram_ok = ram_gb >= 4.0
            
            # CPU check
            cpu_cores = psutil.cpu_count(logical=False)
            cpu_ok = cpu_cores >= 2
            
            # Disk space check
            disk_usage = psutil.disk_usage(str(self.project_root))
            free_gb = disk_usage.free / (1024**3)
            disk_ok = free_gb >= 1.0
            
            # Platform check
            platform_ok = platform.system() in ['Windows', 'Linux', 'Darwin']
            
            all_requirements_met = ram_ok and cpu_ok and disk_ok and platform_ok
            
            details = f"RAM: {ram_gb:.1f}GB, CPU: {cpu_cores} cores, Disk: {free_gb:.1f}GB, Platform: {platform.system()}"
            
            self.check_results['System Requirements'] = {
                'status': all_requirements_met,
                'details': details
            }
            
            return all_requirements_met
            
        except Exception as e:
            self.check_results['System Requirements'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_python_environment(self) -> bool:
        """Check Python environment"""
        try:
            # Python version
            version = sys.version_info[:3]
            version_ok = version >= (3, 8, 0)
            
            # Python executable
            exe_ok = os.path.exists(sys.executable)
            
            python_ok = version_ok and exe_ok
            
            details = f"Python {'.'.join(map(str, version))} at {sys.executable}"
            
            self.check_results['Python Environment'] = {
                'status': python_ok,
                'details': details
            }
            
            return python_ok
            
        except Exception as e:
            self.check_results['Python Environment'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_essential_packages(self) -> bool:
        """Check essential packages (only critical ones)"""
        try:
            # Essential packages for core functionality
            essential_packages = [
                ('PyQt5', 'PyQt5'),
                ('ccxt', 'ccxt'),
                ('pandas', 'pandas'),
                ('numpy', 'numpy'),
                ('requests', 'requests'),
                ('yaml', 'PyYAML'),
                ('psutil', 'psutil'),
                ('websockets', 'websockets'),
                ('aiohttp', 'aiohttp')
            ]
            
            available = []
            missing = []
            
            for import_name, package_name in essential_packages:
                try:
                    __import__(import_name)
                    available.append(package_name)
                except ImportError:
                    missing.append(package_name)
            
            packages_ok = len(missing) == 0
            
            details = f"Available: {len(available)}/{len(essential_packages)}"
            if missing:
                details += f", Missing: {', '.join(missing)}"
            
            self.check_results['Essential Packages'] = {
                'status': packages_ok,
                'details': details
            }
            
            return packages_ok
            
        except Exception as e:
            self.check_results['Essential Packages'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_directory_structure(self) -> bool:
        """Check directory structure"""
        try:
            required_dirs = ['logs', 'cache', 'data', 'config', 'backups']
            
            existing = []
            missing = []
            
            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    existing.append(dir_name)
                else:
                    missing.append(dir_name)
                    # Try to create
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        existing.append(dir_name)
                        missing.remove(dir_name)
                    except:
                        pass
            
            dirs_ok = len(missing) == 0
            
            details = f"Existing: {len(existing)}/{len(required_dirs)}"
            if missing:
                details += f", Missing: {', '.join(missing)}"
            
            self.check_results['Directory Structure'] = {
                'status': dirs_ok,
                'details': details
            }
            
            return dirs_ok
            
        except Exception as e:
            self.check_results['Directory Structure'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_configuration_files(self) -> bool:
        """Check configuration files"""
        try:
            config_files = [
                'config/trading_config.py',
                'credentials.py'
            ]
            
            existing = []
            missing = []
            
            for config_file in config_files:
                file_path = self.project_root / config_file
                if file_path.exists():
                    existing.append(config_file)
                else:
                    missing.append(config_file)
            
            # At least trading_config.py must exist
            essential_exists = (self.project_root / 'config/trading_config.py').exists()
            
            details = f"Existing: {len(existing)}/{len(config_files)}"
            if missing:
                details += f", Missing: {', '.join(missing)}"
            
            self.check_results['Configuration Files'] = {
                'status': essential_exists,
                'details': details
            }
            
            return essential_exists
            
        except Exception as e:
            self.check_results['Configuration Files'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_network_connectivity(self) -> bool:
        """Check network connectivity"""
        try:
            import requests
            
            # Test internet
            try:
                response = requests.get('https://httpbin.org/status/200', timeout=10)
                internet_ok = response.status_code == 200
            except:
                internet_ok = False
            
            # Test at least one exchange
            exchanges_tested = 0
            exchanges_ok = 0
            
            test_urls = [
                'https://api.binance.com/api/v3/ping',
                'https://www.okx.com/api/v5/public/time'
            ]
            
            for url in test_urls:
                exchanges_tested += 1
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        exchanges_ok += 1
                except:
                    pass
            
            network_ok = internet_ok and exchanges_ok > 0
            
            details = f"Internet: {'OK' if internet_ok else 'FAIL'}, Exchanges: {exchanges_ok}/{exchanges_tested}"
            
            self.check_results['Network Connectivity'] = {
                'status': network_ok,
                'details': details
            }
            
            return network_ok
            
        except Exception as e:
            self.check_results['Network Connectivity'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_component_integration(self) -> bool:
        """Check component integration"""
        try:
            components = [
                'launch_epinnox',
                'symbol_scanner',
                'core.llm_orchestrator',
                'portfolio.portfolio_manager',
                'core.risk_management_system',
                'trading.ccxt_trading_engine'
            ]
            
            working = []
            failed = []
            
            for component in components:
                try:
                    __import__(component)
                    working.append(component)
                except ImportError:
                    failed.append(component)
            
            integration_ok = len(failed) == 0
            
            details = f"Working: {len(working)}/{len(components)}"
            if failed:
                details += f", Failed: {len(failed)}"
            
            self.check_results['Component Integration'] = {
                'status': integration_ok,
                'details': details
            }
            
            return integration_ok
            
        except Exception as e:
            self.check_results['Component Integration'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_trading_system(self) -> bool:
        """Check trading system readiness"""
        try:
            # Check credentials
            cred_exists = (self.project_root / 'credentials.py').exists()
            
            # Check trading config
            config_exists = (self.project_root / 'config/trading_config.py').exists()
            
            # Check log directory
            log_dir_exists = (self.project_root / 'logs').exists()
            
            trading_ready = cred_exists and config_exists and log_dir_exists
            
            details = f"Credentials: {'OK' if cred_exists else 'MISSING'}, "
            details += f"Config: {'OK' if config_exists else 'MISSING'}, "
            details += f"Logs: {'OK' if log_dir_exists else 'MISSING'}"
            
            self.check_results['Trading System'] = {
                'status': trading_ready,
                'details': details
            }
            
            return trading_ready
            
        except Exception as e:
            self.check_results['Trading System'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_performance(self) -> bool:
        """Check system performance"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_ok = cpu_percent < 80
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_ok = memory.percent < 90
            
            performance_ok = cpu_ok and memory_ok
            
            details = f"CPU: {cpu_percent:.1f}%, RAM: {memory.percent:.1f}%"
            
            self.check_results['Performance'] = {
                'status': performance_ok,
                'details': details
            }
            
            return performance_ok
            
        except Exception as e:
            self.check_results['Performance'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _check_security(self) -> bool:
        """Check security aspects"""
        try:
            # Check for conflicting processes
            current_pid = os.getpid()
            conflicts = 0
            
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        if proc.info['cmdline']:
                            cmdline = ' '.join(proc.info['cmdline'])
                            if 'epinnox' in cmdline.lower() and proc.info['pid'] != current_pid:
                                conflicts += 1
            except:
                pass
            
            security_ok = conflicts == 0
            
            details = f"Conflicting processes: {conflicts}"
            
            self.check_results['Security'] = {
                'status': security_ok,
                'details': details
            }
            
            return security_ok
            
        except Exception as e:
            self.check_results['Security'] = {
                'status': False,
                'details': f'Error: {e}'
            }
            return False
    
    def _generate_final_report(self, passed: int, total: int, check_time: float) -> bool:
        """Generate final system check report"""
        print(f"\n🎯 COMPLETE SYSTEM CHECK REPORT")
        print("=" * 60)
        
        success_rate = (passed / total) * 100
        
        print(f"\n📊 CHECK STATISTICS:")
        print(f"   ⏱️ Check Time: {check_time:.2f}s")
        print(f"   📋 Total Checks: {total}")
        print(f"   ✅ Passed: {passed}")
        print(f"   ❌ Failed: {total - passed}")
        print(f"   📈 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for check_name, result in self.check_results.items():
            status_icon = "✅" if result['status'] else "❌"
            print(f"   {status_icon} {check_name}: {result['details']}")
        
        # Determine final status
        critical_failures = total - passed
        
        if critical_failures == 0:
            final_status = "🟢 SYSTEM FULLY READY"
            ready = True
        elif critical_failures <= 2 and success_rate >= 80:
            final_status = "🟡 SYSTEM MOSTLY READY"
            ready = True
        else:
            final_status = "🔴 SYSTEM NEEDS ATTENTION"
            ready = False
        
        print(f"\n🚀 FINAL STATUS: {final_status}")
        
        if ready:
            print("   ✅ Epinnox v6 is ready for deployment")
            print("   💰 System validated for autonomous trading")
            print("   🎯 All critical systems operational")
            print("   🚀 Proceed with confidence!")
        else:
            print("   ❌ Critical issues detected")
            print("   🛠️ Address failures before deployment")
            print("   📋 Review detailed results above")
        
        # Save report
        self._save_check_report(ready, success_rate)
        
        return ready
    
    def _save_check_report(self, ready: bool, success_rate: float):
        """Save system check report"""
        try:
            report_dir = self.project_root / 'logs' / 'system'
            report_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"complete_system_check_{timestamp}.log"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"Epinnox v6 Complete System Check Report\n")
                f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"System Ready: {ready}\n")
                f.write(f"Success Rate: {success_rate:.1f}%\n\n")
                
                f.write("Detailed Results:\n")
                for check_name, result in self.check_results.items():
                    status = "PASS" if result['status'] else "FAIL"
                    f.write(f"  {check_name}: {status} - {result['details']}\n")
            
            print(f"\n📄 System check report saved: {report_file}")
            
        except Exception as e:
            print(f"⚠️ Could not save system check report: {e}")

def main():
    """Main execution for complete system check"""
    checker = CompleteSystemCheck()
    success = checker.run_complete_system_check()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
