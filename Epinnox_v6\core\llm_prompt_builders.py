"""
LLM Prompt Builders - Specialized prompts for different trading scenarios
Each prompt is optimized for specific trading decisions and market conditions
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class LLMPromptBuilders:
    """
    Collection of specialized prompt builders for different trading scenarios
    Each prompt is designed for maximum effectiveness in specific situations
    """
    
    def __init__(self, trading_interface):
        self.trading_interface = trading_interface
    
    def build_emergency_response_prompt(self, context) -> str:
        """Build emergency response prompt for crisis situations"""
        
        emergency_types = ", ".join(context.emergency_flags) if context.emergency_flags else "NONE"
        
        positions_summary = ""
        total_risk = 0
        if context.open_positions:
            for pos in context.open_positions:
                pnl = pos.get('unrealized_pnl', 0)
                risk_amount = abs(pnl) if pnl < 0 else 0
                total_risk += risk_amount
                positions_summary += f"• {pos['symbol']}: {pos['side']} {pos['size']:.2f} (PnL: ${pnl:.2f})\n"
        else:
            positions_summary = "• No open positions"
        
        prompt = f"""🚨 EMERGENCY RESPONSE SYSTEM - IMMEDIATE ACTION REQUIRED

⚠️ EMERGENCY FLAGS: {emergency_types}
💰 ACCOUNT: ${context.account_balance:.2f} | RISK: ${total_risk:.2f}
📊 POSITIONS ({len(context.open_positions)}):
{positions_summary}

📈 MARKET CONDITIONS:
Price: ${context.current_price:.6f} | Spread: {context.market_data.get('spread_pct', 0):.3f}%
Volume: {context.market_data.get('volume_spike', 'NORMAL')} | Volatility: {context.market_data.get('volatility', 'NORMAL')}

🎯 EMERGENCY PROTOCOLS:
- CLOSE_ALL: Close all positions immediately (market orders)
- CLOSE_LOSING: Close only losing positions
- HEDGE: Open hedge positions to reduce risk
- MONITOR: Continue monitoring, no immediate action
- REDUCE_SIZE: Reduce position sizes by 50%

EMERGENCY DECISION - JSON ONLY:
{{"ACTION":"MONITOR","PRIORITY":"MEDIUM","REASONING":"No immediate threat detected","CONFIDENCE":85}}

ACTION: CLOSE_ALL/CLOSE_LOSING/HEDGE/MONITOR/REDUCE_SIZE | PRIORITY: IMMEDIATE/HIGH/MEDIUM"""
        
        return prompt
    
    def build_position_management_prompt(self, context) -> str:
        """Build position management prompt for active positions"""
        
        if not context.open_positions:
            return ""
        
        # Focus on the most critical position (largest loss or oldest)
        critical_position = self.get_most_critical_position(context.open_positions)
        
        entry_price = critical_position.get('entry_price', 0)
        current_price = context.current_price
        side = critical_position.get('side', 'UNKNOWN')
        size = critical_position.get('size', 0)
        unrealized_pnl = critical_position.get('unrealized_pnl', 0)
        time_held = critical_position.get('time_held', 0)
        
        # Calculate key metrics
        if entry_price > 0:
            price_move_pct = ((current_price - entry_price) / entry_price) * 100
            if side.upper() == 'SHORT':
                price_move_pct *= -1
        else:
            price_move_pct = 0
        
        pnl_pct = (unrealized_pnl / (entry_price * size)) * 100 if entry_price * size > 0 else 0
        
        prompt = f"""🎯 POSITION MANAGER - {critical_position['symbol']} {side.upper()} ACTIVE

📊 POSITION STATUS:
Entry: ${entry_price:.6f} | Current: ${current_price:.6f} | Size: {size:.2f}
PnL: ${unrealized_pnl:.2f} ({pnl_pct:+.2f}%) | Time: {time_held//60}m{time_held%60}s
Price Move: {price_move_pct:+.2f}% | Target: +0.5-2.0% | Stop: -0.3%

📈 MARKET CONDITIONS:
Spread: {context.market_data.get('spread_pct', 0):.3f}% | Momentum: {context.market_data.get('momentum', 0):+.1f}%
Volume: {context.market_data.get('volume_trend', 'NORMAL')} | Volatility: {context.market_data.get('volatility', 2.0):.1f}%
Support: ${context.market_data.get('support_level', current_price * 0.995):.6f}
Resistance: ${context.market_data.get('resistance_level', current_price * 1.005):.6f}

⚡ SCALPING RULES:
- TAKE PROFIT: 0.5-2.0% gains (adjust for volatility)
- STOP LOSS: -0.3% max loss
- TIME LIMIT: 5-15 minutes max hold
- TRAIL STOPS: Move stops to breakeven at +0.8% profit

💰 ACCOUNT: ${context.account_balance:.2f} | Total Positions: {len(context.open_positions)}

POSITION DECISION - JSON ONLY:
{{"ACTION":"HOLD","REASON":"monitoring momentum","STOP_LOSS":{current_price * 0.997:.6f},"TAKE_PROFIT":{current_price * 1.015:.6f},"TRAIL_STOP":false,"URGENCY":"LOW","CONFIDENCE":75}}

ACTION: HOLD/CLOSE/PARTIAL_CLOSE | STOP_LOSS: price | TAKE_PROFIT: price | TRAIL_STOP: true/false"""
        
        return prompt
    
    def build_profit_optimization_prompt(self, context) -> str:
        """Build profit optimization prompt for profitable positions"""
        
        profitable_positions = [pos for pos in context.open_positions if pos.get('unrealized_pnl', 0) > 0]
        
        if not profitable_positions:
            return ""
        
        # Focus on most profitable position
        best_position = max(profitable_positions, key=lambda x: x.get('unrealized_pnl', 0))
        
        entry_price = best_position.get('entry_price', 0)
        current_price = context.current_price
        unrealized_pnl = best_position.get('unrealized_pnl', 0)
        size = best_position.get('size', 0)
        time_held = best_position.get('time_held', 0)
        
        pnl_pct = (unrealized_pnl / (entry_price * size)) * 100 if entry_price * size > 0 else 0
        
        # Calculate profit targets
        target_1 = current_price * 1.005  # 0.5% above current
        target_2 = current_price * 1.010  # 1.0% above current
        target_3 = current_price * 1.020  # 2.0% above current
        
        prompt = f"""💰 PROFIT OPTIMIZATION - {best_position['symbol']} ({pnl_pct:+.2f}% PROFIT)

📊 POSITION STATUS:
Entry: ${entry_price:.6f} | Current: ${current_price:.6f} | Size: {size:.2f}
Unrealized PnL: ${unrealized_pnl:.2f} ({pnl_pct:+.2f}%)
Time Held: {time_held//60}m{time_held%60}s

🎯 PROFIT TARGETS:
Target 1: ${target_1:.6f} (+0.5%) - Conservative take
Target 2: ${target_2:.6f} (+1.0%) - Moderate take  
Target 3: ${target_3:.6f} (+2.0%) - Aggressive hold

📈 MARKET CONDITIONS:
Momentum: {context.market_data.get('momentum', 0):+.1f}% | Volume: {context.market_data.get('volume_trend', 'NORMAL')}
Next Resistance: ${context.market_data.get('resistance_level', current_price * 1.01):.6f}
Next Support: ${context.market_data.get('support_level', current_price * 0.99):.6f}

⚡ PROFIT STRATEGIES:
- HOLD: Continue holding for higher targets
- PARTIAL_CLOSE: Take 25-75% profit, let rest run
- FULL_CLOSE: Take all profit now
- TRAIL_STOP: Set trailing stop to protect gains

PROFIT DECISION - JSON ONLY:
{{"ACTION":"PARTIAL_CLOSE","CLOSE_PERCENTAGE":50,"NEW_STOP":{entry_price * 1.002:.6f},"TRAIL_STOP":true,"REASONING":"Take 50% at resistance","CONFIDENCE":80}}

ACTION: HOLD/PARTIAL_CLOSE/FULL_CLOSE | CLOSE_PERCENTAGE: 0-100 | TRAIL_STOP: true/false"""
        
        return prompt
    
    def build_market_regime_prompt(self, context) -> str:
        """Build market regime detection prompt"""
        
        # Get multi-timeframe data
        timeframes = ['1m', '5m', '15m']
        tf_data = {}
        
        for tf in timeframes:
            tf_data[tf] = context.market_data.get(tf, {
                'trend': 'NEUTRAL',
                'volume': 1.0,
                'momentum': 0.0,
                'volatility': 2.0
            })
        
        prompt = f"""📊 MARKET REGIME ANALYST - {context.symbol}

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend={tf_data['1m']['trend']} | Vol={tf_data['1m']['volume']:.1f}x | Mom={tf_data['1m']['momentum']:+.1f}%
5m: Trend={tf_data['5m']['trend']} | Vol={tf_data['5m']['volume']:.1f}x | Mom={tf_data['5m']['momentum']:+.1f}%
15m: Trend={tf_data['15m']['trend']} | Vol={tf_data['15m']['volume']:.1f}x | Mom={tf_data['15m']['momentum']:+.1f}%

📈 VOLATILITY METRICS:
ATR: {context.market_data.get('atr', 0.001):.6f} | Spread: {context.market_data.get('spread_pct', 0.1):.3f}%
Volume Ratio: {context.market_data.get('volume_ratio', 1.0):.1f}x | Price Range: {context.market_data.get('price_range_pct', 1.0):.2f}%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow price action, consolidation
- RANGING_VOLATILE: High volatility, no clear direction, choppy
- BREAKOUT_PENDING: Consolidation before major move, building pressure
- NEWS_DRIVEN: Unusual volume/volatility spikes, event-driven

⚡ SCALPING SUITABILITY:
- HIGH: Perfect for scalping (clear trends, good volume)
- MEDIUM: Acceptable for scalping (some challenges)
- LOW: Avoid scalping (dangerous conditions)

REGIME ANALYSIS - JSON ONLY:
{{"REGIME":"RANGING_TIGHT","CONFIDENCE":75,"SCALP_SUITABILITY":"MEDIUM","RECOMMENDED_TIMEFRAME":"1m","RISK_LEVEL":"MEDIUM","REASONING":"Consolidation phase with moderate volatility"}}

REGIME: classification | CONFIDENCE: 0-100 | SCALP_SUITABILITY: HIGH/MEDIUM/LOW"""
        
        return prompt
    
    def get_most_critical_position(self, positions: List[Dict]) -> Dict:
        """Get the most critical position requiring immediate attention"""
        
        if not positions:
            return {}
        
        # Priority: Largest loss, then oldest position
        losing_positions = [pos for pos in positions if pos.get('unrealized_pnl', 0) < 0]
        
        if losing_positions:
            # Return position with largest loss
            return min(losing_positions, key=lambda x: x.get('unrealized_pnl', 0))
        else:
            # Return oldest position
            return max(positions, key=lambda x: x.get('time_held', 0))

    def build_risk_assessment_prompt(self, context=None) -> str:
        """🚨 ENHANCED: Build risk assessment prompt with intelligent account preservation logic"""

        # Calculate proposed trade risk metrics
        proposed_trade = context.market_data.get('proposed_trade', {})

        if not proposed_trade:
            return ""

        risk_amount = proposed_trade.get('risk_amount', 0)
        risk_pct = (risk_amount / context.account_balance) * 100 if context.account_balance > 0 else 0

        # 🚨 ACCOUNT HEALTH ASSESSMENT
        account_health = self.assess_account_health_for_prompt(context.account_balance)

        # Load thresholds for display (same logic as in assess_account_health_for_prompt)
        emergency_threshold = 5.0
        critical_threshold = 10.0
        warning_threshold = 20.0
        healthy_threshold = 50.0
        
        try:
            if os.path.exists('small_balance_override.json'):
                with open('small_balance_override.json', 'r') as f:
                    override_config = json.load(f)
                    if override_config.get('minimum_balance_override', False):
                        override_min = override_config.get('override_minimum', 10.0)
                        emergency_override = override_config.get('emergency_balance', 2.0)
                        
                        emergency_threshold = emergency_override
                        critical_threshold = override_min
                        warning_threshold = override_min * 2.0
                        healthy_threshold = override_min * 5.0
        except Exception:
            pass  # Use default thresholds

        # Calculate total portfolio exposure
        total_exposure = sum(abs(pos.get('notional', 0)) for pos in context.open_positions)
        exposure_pct = (total_exposure / context.account_balance) * 100 if context.account_balance > 0 else 0

        # Calculate liquidation risk
        new_exposure = total_exposure + proposed_trade.get('notional_value', 0)
        liquidation_risk_pct = (new_exposure / context.account_balance) * 100 if context.account_balance > 0 else 100

        # Calculate position correlation
        correlation_risk = self.calculate_position_correlation(context.open_positions, proposed_trade)

        prompt = f"""🛡️ INTELLIGENT RISK MANAGEMENT - ACCOUNT PRESERVATION PRIORITY

🚨 ACCOUNT HEALTH STATUS: {account_health['status']}
💰 Balance: ${context.account_balance:.2f} | Health: {account_health['description']}
📊 Current Exposure: ${total_exposure:.2f} ({exposure_pct:.1f}% of account)
⚠️ Liquidation Risk: {liquidation_risk_pct:.1f}% (after proposed trade)

🚨 CRITICAL ACCOUNT THRESHOLDS:
- Emergency Stop: Below ${emergency_threshold:.2f} (NO TRADING)
- Capital Preservation: Below ${critical_threshold:.2f} (NO NEW TRADES)
- High Risk Mode: Below ${warning_threshold:.2f} (0.5% max risk per trade)
- Moderate Risk: Below ${healthy_threshold:.2f} (1.0% max risk per trade)
- Healthy: Above ${healthy_threshold:.2f} (2.0% max risk per trade)

📊 PROPOSED TRADE ANALYSIS:
Symbol: {proposed_trade.get('symbol', context.symbol)} | Side: {proposed_trade.get('side', 'UNKNOWN')}
Size: {proposed_trade.get('quantity', 0):.2f} | Entry: ${proposed_trade.get('entry_price', context.current_price):.6f}
Stop Loss: ${proposed_trade.get('stop_loss', 0):.6f} | Take Profit: ${proposed_trade.get('take_profit', 0):.6f}
Risk Amount: ${risk_amount:.2f} | Risk %: {risk_pct:.2f}%
Notional Value: ${proposed_trade.get('notional_value', 0):.2f}

⚠️ COMPREHENSIVE RISK FACTORS:
- Account Health: {account_health['status']} (Risk Multiplier: {account_health['risk_multiplier']:.2f})
- Portfolio Exposure: {exposure_pct:.1f}% → {liquidation_risk_pct:.1f}% (after trade)
- Position Correlation: {correlation_risk:.1f}% (with existing positions)
- Market Volatility: {context.market_data.get('volatility', 2.0):.1f}% (vs normal 2%)
- Liquidity Risk: {context.market_data.get('spread_pct', 0.1):.3f}% spread
- Position Count: {len(context.open_positions)} existing positions
- Time of Day: {datetime.now().strftime('%H:%M')} (market hours risk)

🎯 DYNAMIC RISK LIMITS (Based on Account Health):
- Max Risk per Trade: {account_health['max_risk_pct']:.1f}% of account
- Max Total Exposure: {account_health['max_exposure_pct']:.1f}% of account
- Max Concurrent Positions: {account_health['max_positions']}
- Max Correlation: 70% with existing positions
- Liquidation Risk Limit: 80% total exposure

🚨 ACCOUNT PRESERVATION RULES:
1. If balance < ${emergency_threshold:.2f}: REJECT ALL TRADES (Emergency Stop)
2. If balance < ${critical_threshold:.2f}: REJECT NEW TRADES (Capital Preservation)
3. If exposure > 80%: REJECT TRADE (Liquidation Risk)
4. If correlation > 70%: REDUCE SIZE or REJECT
5. Prioritize ACCOUNT SURVIVAL over profit maximization

INTELLIGENT RISK VERDICT - JSON ONLY:
{{"APPROVED":{str(account_health['allow_trading']).lower()},"RISK_SCORE":{min(95, max(5, int(liquidation_risk_pct))):.0f},"ADJUSTMENTS":"{account_health['required_adjustments']}","MAX_POSITION_SIZE":{account_health['max_position_size']:.0f},"REASONING":"{account_health['reasoning']}","CONFIDENCE":{account_health['confidence']:.0f},"ACCOUNT_STATUS":"{account_health['status']}"}}

APPROVED: true/false | RISK_SCORE: 0-100 | ACCOUNT_STATUS: health level | ADJUSTMENTS: required changes"""
        if context:
            last5 = [p for _, p in context['recent_prices'][-5:]]
            sig5  = [s['decision'] for s in context['recent_signals'][-5:]]
            prompt += "\n# HISTORICAL CONTEXT\n"
            prompt += f"- Last 5 prices: {last5}\n"
            prompt += f"- Last 5 signals: {sig5}\n"
        return prompt

    def assess_account_health_for_prompt(self, balance):
        """🚨 Assess account health for prompt building"""
        try:
            # Load thresholds from override config if available
            emergency_threshold = 5.0      # Below $5 = Emergency stop
            critical_threshold = 10.0      # Below $10 = Capital preservation (default)
            warning_threshold = 20.0       # Below $20 = Reduced risk
            healthy_threshold = 50.0       # Above $50 = Normal operation
            
            try:
                if os.path.exists('small_balance_override.json'):
                    with open('small_balance_override.json', 'r') as f:
                        override_config = json.load(f)
                        if override_config.get('minimum_balance_override', False):
                            # Adjust thresholds based on override config
                            override_min = override_config.get('override_minimum', 10.0)
                            emergency_override = override_config.get('emergency_balance', 2.0)
                            
                            emergency_threshold = emergency_override
                            critical_threshold = override_min
                            # Scale other thresholds proportionally
                            warning_threshold = override_min * 2.0
                            healthy_threshold = override_min * 5.0
            except Exception as e:
                pass  # Use default thresholds on error
            healthy_threshold = 50.0       # Above $50 = Normal operation

            if balance < emergency_threshold:
                return {
                    'status': 'EMERGENCY_STOP',
                    'description': 'Critical - Emergency Stop Required',
                    'allow_trading': False,
                    'risk_multiplier': 0.0,
                    'max_risk_pct': 0.0,
                    'max_exposure_pct': 0.0,
                    'max_positions': 0,
                    'max_position_size': 0,
                    'required_adjustments': 'EMERGENCY_STOP_ALL_TRADING',
                    'reasoning': 'Account balance critically low - emergency stop to preserve remaining capital',
                    'confidence': 100
                }
            elif balance < critical_threshold:
                return {
                    'status': 'CAPITAL_PRESERVATION',
                    'description': 'Critical - No New Trades',
                    'allow_trading': False,
                    'risk_multiplier': 0.0,
                    'max_risk_pct': 0.0,
                    'max_exposure_pct': 0.0,
                    'max_positions': 0,
                    'max_position_size': 0,
                    'required_adjustments': 'NO_NEW_TRADES_PRESERVE_CAPITAL',
                    'reasoning': 'Account balance too low for new trades - focus on preserving remaining capital',
                    'confidence': 95
                }
            elif balance < warning_threshold:
                return {
                    'status': 'HIGH_RISK',
                    'description': 'High Risk - Minimal Trading Only',
                    'allow_trading': True,
                    'risk_multiplier': 0.25,
                    'max_risk_pct': 0.5,
                    'max_exposure_pct': 30.0,
                    'max_positions': 1,
                    'max_position_size': max(1, balance * 0.01),  # 1% of balance max
                    'required_adjustments': 'REDUCE_SIZE_EXTREME_CAUTION',
                    'reasoning': 'Account in high-risk zone - only minimal positions with extreme caution',
                    'confidence': 90
                }
            elif balance < healthy_threshold:
                return {
                    'status': 'MODERATE_RISK',
                    'description': 'Moderate Risk - Reduced Trading',
                    'allow_trading': True,
                    'risk_multiplier': 0.5,
                    'max_risk_pct': 1.0,
                    'max_exposure_pct': 50.0,
                    'max_positions': 2,
                    'max_position_size': max(1, balance * 0.02),  # 2% of balance max
                    'required_adjustments': 'REDUCE_SIZE_MODERATE_CAUTION',
                    'reasoning': 'Account in moderate risk zone - reduced position sizes recommended',
                    'confidence': 85
                }
            else:
                return {
                    'status': 'HEALTHY',
                    'description': 'Healthy - Normal Trading',
                    'allow_trading': True,
                    'risk_multiplier': 1.0,
                    'max_risk_pct': 2.0,
                    'max_exposure_pct': 70.0,
                    'max_positions': 3,
                    'max_position_size': max(1, balance * 0.04),  # 4% of balance max
                    'required_adjustments': 'NONE',
                    'reasoning': 'Account healthy - normal trading parameters acceptable',
                    'confidence': 80
                }

        except Exception:
            # Return ultra-conservative defaults on error
            return {
                'status': 'ERROR_CONSERVATIVE',
                'description': 'Error - Ultra Conservative Mode',
                'allow_trading': False,
                'risk_multiplier': 0.1,
                'max_risk_pct': 0.1,
                'max_exposure_pct': 10.0,
                'max_positions': 1,
                'max_position_size': 1,
                'required_adjustments': 'ERROR_ULTRA_CONSERVATIVE',
                'reasoning': 'Error in assessment - defaulting to ultra-conservative parameters',
                'confidence': 50
            }

    def build_entry_timing_prompt(self, context=None) -> str:
        """Build entry timing optimization prompt"""

        signal_data = context.market_data.get('signals', {})

        prompt = f"""🎯 ENTRY TIMING SPECIALIST - {context.symbol}

🎯 SIGNAL ANALYSIS:
ML Ensemble: {signal_data.get('ensemble_decision', 'NEUTRAL')} ({signal_data.get('confidence', 50):.1f}%)
Technical: {signal_data.get('technical_signal', 'NEUTRAL')} | Momentum: {signal_data.get('momentum_score', 0):.2f}
Volume: {signal_data.get('volume_confirmation', 'PENDING')} | Orderflow: {signal_data.get('orderflow_bias', 'NEUTRAL')}

📊 MARKET MICROSTRUCTURE:
Bid/Ask: ${context.market_data.get('bid', context.current_price * 0.9995):.6f}/${context.market_data.get('ask', context.current_price * 1.0005):.6f}
Spread: {context.market_data.get('spread_pct', 0.1):.3f}% | L2 Imbalance: {context.market_data.get('depth_imbalance', 0):.1f}%
Recent Flow: {context.market_data.get('trade_flow', 'BALANCED')} | Volume Spike: {context.market_data.get('volume_spike', False)}

📈 KEY LEVELS:
Support: ${context.market_data.get('nearest_support', context.current_price * 0.995):.6f}
Resistance: ${context.market_data.get('nearest_resistance', context.current_price * 1.005):.6f}
Distance to Support: {((context.current_price - context.market_data.get('nearest_support', context.current_price * 0.995)) / context.current_price) * 100:.2f}%

⏰ TIMING FACTORS:
- Price near key levels: {self.check_price_near_levels(context)}
- Volume confirmation: {signal_data.get('volume_confirmation', 'PENDING')}
- Spread conditions: {'FAVORABLE' if context.market_data.get('spread_pct', 0.1) < 0.2 else 'UNFAVORABLE'}
- Momentum alignment: {signal_data.get('momentum_alignment', 'NEUTRAL')}
- Risk/reward ratio: {context.market_data.get('risk_reward_ratio', 2.0):.1f}:1

ENTRY DECISION - JSON ONLY:
{{"ACTION":"WAIT","ENTRY_TYPE":"LIMIT","CONFIDENCE":65,"WAIT_FOR":"VOLUME_CONFIRMATION","MAX_WAIT_SECONDS":60,"REASONING":"Waiting for volume confirmation"}}

ACTION: ENTER_NOW/WAIT/ABORT | ENTRY_TYPE: MARKET/LIMIT | WAIT_FOR: condition"""
        if context:
            last5 = [p for _, p in context['recent_prices'][-5:]]
            sig5  = [s['decision'] for s in context['recent_signals'][-5:]]
            prompt += "\n# HISTORICAL CONTEXT\n"
            prompt += f"- Last 5 prices: {last5}\n"
            prompt += f"- Last 5 signals: {sig5}\n"
        return prompt

    def build_strategy_adaptation_prompt(self, context) -> str:
        """Build strategy adaptation prompt"""

        perf = context.performance_metrics

        prompt = f"""🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: {perf.get('trades_24h', 0)} | Win Rate: {perf.get('win_rate_24h', 50):.1f}%
Avg Profit: {perf.get('avg_profit', 0.8):.2f}% | Avg Loss: {perf.get('avg_loss', -0.3):.2f}%
Sharpe Ratio: {perf.get('sharpe_ratio', 1.0):.2f} | Max Drawdown: {perf.get('max_dd', 0):.1f}%
Total PnL: ${perf.get('total_pnl_24h', 0):.2f} | ROI: {perf.get('roi_24h', 0):.1f}%

🎯 CURRENT STRATEGY:
Risk per Trade: {perf.get('current_risk_pct', 2.0):.1f}% | Avg Hold Time: {perf.get('avg_hold_time', 8):.1f}min
Entry Threshold: {perf.get('entry_threshold', 70):.0f}% | Exit Threshold: {perf.get('exit_threshold', 60):.0f}%
Position Size Method: {perf.get('sizing_method', 'FIXED_RISK')} | Max Positions: {perf.get('max_positions', 3)}

📈 MARKET REGIME: {context.market_data.get('current_regime', 'UNKNOWN')}
Regime Confidence: {context.market_data.get('regime_confidence', 50):.1f}%
Scalp Suitability: {context.market_data.get('scalp_suitability', 'MEDIUM')}

🔧 ADAPTATION FACTORS:
- Win rate trending: {'UP' if perf.get('win_rate_trend', 0) > 0 else 'DOWN'}
- Drawdown level: {'HIGH' if perf.get('max_dd', 0) > 5 else 'NORMAL'}
- Market volatility: {'HIGH' if context.market_data.get('volatility', 2) > 3 else 'NORMAL'}
- Recent performance: {'GOOD' if perf.get('recent_pnl', 0) > 0 else 'POOR'}

STRATEGY ADJUSTMENTS - JSON ONLY:
{{"RISK_ADJUSTMENT":1.0,"HOLD_TIME_TARGET":8,"ENTRY_THRESHOLD":70,"EXIT_THRESHOLD":60,"SIZING_METHOD":"FIXED_RISK","REASONING":"Maintain current strategy - performing well","CONFIDENCE":75}}

RISK_ADJUSTMENT: 0.5-2.0 multiplier | HOLD_TIME_TARGET: minutes | ENTRY_THRESHOLD: confidence %"""

        return prompt

    def build_opportunity_scanner_prompt(self, context=None) -> str:
        """🚨 ENHANCED: Build opportunity scanner prompt with account health awareness"""

        # 🚨 ACCOUNT HEALTH ASSESSMENT
        account_health = self.assess_account_health_for_prompt(context.account_balance)

        # Calculate total portfolio exposure
        total_exposure = sum(abs(pos.get('notional', 0)) for pos in context.open_positions)
        exposure_pct = (total_exposure / context.account_balance) * 100 if context.account_balance > 0 else 0

        # Get data for multiple symbols with real price fetching
        symbols = ['DOGE/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT', 'SOL/USDT:USDT']
        symbol_data = {}

        for symbol in symbols:
            # Get real price data for each symbol
            if symbol == context.symbol:
                price = context.current_price
                momentum = context.market_data.get('momentum', 0)
                volume = context.market_data.get('volume_ratio', 1.0)
                volatility = context.market_data.get('volatility', 2.0)
            else:
                # Fetch real market data for other symbols
                try:
                    # Try to get real price from market data or use a reasonable default
                    if hasattr(context, 'market_api') and context.market_api:
                        ticker = context.market_api.fetch_ticker(symbol)
                        price = ticker['last']
                        momentum = 0  # Default for other symbols
                        volume = 1.0  # Default volume ratio
                        volatility = 2.0  # Default volatility
                    else:
                        # Fallback to symbol-specific defaults if no market API
                        symbol_defaults = {
                            'BTC/USDT:USDT': 95000.0,
                            'ETH/USDT:USDT': 3500.0,
                            'SOL/USDT:USDT': 200.0
                        }
                        price = symbol_defaults.get(symbol, context.current_price * 0.95)
                        momentum = 0
                        volume = 1.0
                        volatility = 2.0
                except Exception as e:
                    # Fallback to reasonable defaults if fetching fails
                    symbol_defaults = {
                        'BTC/USDT:USDT': 95000.0,
                        'ETH/USDT:USDT': 3500.0,
                        'SOL/USDT:USDT': 200.0
                    }
                    price = symbol_defaults.get(symbol, context.current_price * 0.95)
                    momentum = 0
                    volume = 1.0
                    volatility = 2.0

            symbol_data[symbol] = {
                'price': price,
                'momentum': momentum,
                'volume': volume,
                'volatility': volatility,
                'setup_quality': 'MEDIUM'
            }

        prompt = f"""🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: {account_health['status']}
💰 Balance: ${context.account_balance:.2f} | Health: {account_health['description']}
📊 Current Exposure: ${total_exposure:.2f} ({exposure_pct:.1f}% of account)
🎯 Trading Allowed: {'YES' if account_health['allow_trading'] else 'NO'}

📊 SYMBOL ANALYSIS:
{self.format_symbol_data_for_prompt(symbol_data)}

🎯 MARKET OVERVIEW:
Sector Momentum: {context.market_data.get('sector_momentum', 'NEUTRAL')}
Overall Volatility: {context.market_data.get('market_volatility', 'NORMAL')}
Volume Profile: {context.market_data.get('volume_profile', 'AVERAGE')}
Risk Environment: {context.market_data.get('risk_environment', 'MODERATE')}

💰 ACCOUNT CAPACITY (Health-Adjusted):
Available Balance: ${context.account_balance:.2f}
Max Position Size: ${account_health['max_position_size']:.2f}
Position Slots: {account_health['max_positions'] - len(context.open_positions)}/{account_health['max_positions']} available
Risk Budget: {account_health['max_risk_pct']:.1f}% per trade (health-adjusted)
Max Total Exposure: {account_health['max_exposure_pct']:.1f}% of account

🚨 ACCOUNT PRESERVATION CRITERIA:
- Account Health: {account_health['status']} (Priority: SURVIVAL over PROFIT)
- Max Risk per Trade: {account_health['max_risk_pct']:.1f}% of account
- Max Total Exposure: {account_health['max_exposure_pct']:.1f}% of account
- Position Limit: {account_health['max_positions']} concurrent positions
- Trading Status: {'ALLOWED' if account_health['allow_trading'] else 'SUSPENDED'}

🎯 OPPORTUNITY CRITERIA (Health-Adjusted):
- Setup Quality: HIGH (clear patterns, strong signals)
- Risk/Reward: >3:1 ratio required (higher due to account health)
- Liquidity: Excellent spreads (<0.2%) required
- Momentum: Strong alignment required
- Volatility: Conservative range (1-3%) preferred
- Account Impact: Must not exceed health-based limits

INTELLIGENT OPPORTUNITY RANKING - JSON ONLY:
{{"BEST_OPPORTUNITY":"{'NONE' if not account_health['allow_trading'] else context.symbol}","CONFIDENCE":{account_health['confidence']:.0f},"SETUP_TYPE":"{'ACCOUNT_PRESERVATION' if not account_health['allow_trading'] else 'CONSERVATIVE_ENTRY'}","TIME_HORIZON":"{'INDEFINITE' if not account_health['allow_trading'] else '5-15min'}","RISK_REWARD":"{'N/A' if not account_health['allow_trading'] else '1:3.0'}","REASONING":"{account_health['reasoning']}","ACCOUNT_STATUS":"{account_health['status']}"}}

BEST_OPPORTUNITY: symbol or NONE | ACCOUNT_STATUS: health level | REASONING: account-aware decision"""
        if context:
            last5 = [p for _, p in context['recent_prices'][-5:]]
            sig5  = [s['decision'] for s in context['recent_signals'][-5:]]
            prompt += "\n# HISTORICAL CONTEXT\n"
            prompt += f"- Last 5 prices: {last5}\n"
            prompt += f"- Last 5 signals: {sig5}\n"
        return prompt

    def calculate_position_correlation(self, positions: List[Dict], proposed_trade: Dict) -> float:
        """Calculate correlation risk with existing positions"""

        if not positions:
            return 0.0

        # Simple correlation based on same direction trades
        proposed_side = proposed_trade.get('side', 'UNKNOWN')
        same_direction_count = sum(1 for pos in positions if pos.get('side') == proposed_side)

        correlation_pct = (same_direction_count / len(positions)) * 100
        return correlation_pct

    def check_price_near_levels(self, context) -> str:
        """Check if price is near key support/resistance levels"""

        current_price = context.current_price
        support = context.market_data.get('nearest_support', current_price * 0.995)
        resistance = context.market_data.get('nearest_resistance', current_price * 1.005)

        support_distance = abs(current_price - support) / current_price * 100
        resistance_distance = abs(resistance - current_price) / current_price * 100

        if support_distance < 0.1 or resistance_distance < 0.1:
            return "VERY_CLOSE"
        elif support_distance < 0.3 or resistance_distance < 0.3:
            return "CLOSE"
        else:
            return "NEUTRAL"

    def format_symbol_data_for_prompt(self, symbol_data: Dict) -> str:
        """Format symbol data for opportunity scanner prompt"""

        formatted = ""
        for symbol, data in symbol_data.items():
            formatted += f"• {symbol}: ${data['price']:.6f} | Mom: {data['momentum']:+.1f}% | Vol: {data['volume']:.1f}x | Setup: {data['setup_quality']}\n"

        return formatted.strip()

def build_auto_trade_prompt(symbol: str,
    main_candles: list,
    candles_5m: list,
    candles_15m: list,
    bands: dict,
    spread: float,
    volume_24h: float,
    current_margin: tuple,
    position_info: dict,
    cache_context: dict,
    version: str = "v1.0") -> str:
    """
    Constructs the full prompt for llm_decision_auto_trade - SCALPING FOCUSED
    """
    import datetime
    ts = datetime.datetime.utcnow().isoformat()
    
    # Calculate scalping metrics
    last_price = main_candles[-1]['close'] if main_candles else 0
    volume_ratio = volume_24h / 1000000 if volume_24h > 0 else 1.0  # Normalize volume
    
    # Get recent momentum from last few candles
    recent_momentum = "NEUTRAL"
    if len(main_candles) >= 3:
        price_change = ((main_candles[-1]['close'] - main_candles[-3]['close']) / main_candles[-3]['close']) * 100
        if price_change > 0.1:
            recent_momentum = "BULLISH"
        elif price_change < -0.1:
            recent_momentum = "BEARISH"
    
    prompt = f"""🚀 SCALPER DECISION ENGINE - {symbol} - EPINNOX V6 🚀
# Timestamp: {ts} | Version: {version}

⚡ SCALPING CONTEXT:
Symbol: {symbol} | Price: ${last_price:.6f}
Spread: {spread:.4f}% | Volume: {volume_ratio:.1f}x avg
Momentum: {recent_momentum} | Margin Used: {current_margin[1]:.1f}%

📊 TIMEFRAME ANALYSIS:
## 1m Candles (last 50 - PRIMARY SCALPING TIMEFRAME):
{main_candles}

## 5m Candles (last 10 - TREND CONTEXT):
{candles_5m}

## 15m Candles (last 10 - MAJOR TREND):
{candles_15m}

🎯 TECHNICAL INDICATORS:
1m: EMA={bands['main']['ema']:.6f} | ATR={bands['main']['atr']:.6f}
5m: EMA={bands['5m']['ema']:.6f} | ATR={bands['5m']['atr']:.6f}  
15m: EMA={bands['15m']['ema']:.6f} | ATR={bands['15m']['atr']:.6f}

💰 POSITION STATUS:
{position_info}

📈 SCALPING MEMORY:
Recent Prices: {cache_context.get('recent_prices', [])}
Recent Signals: {cache_context.get('recent_signals', [])}
Trade Results: {cache_context.get('trade_outcomes', [])}

⚡ SCALPING RULES - MANDATORY:
1. Target 2-15 pip moves (0.5-3% profit)
2. Maximum 5-minute hold time
3. NEVER suggest WAIT unless spread > 0.5% or volume < 0.5x
4. Minimum 2:1 risk/reward ratio
5. Stop loss: 0.3-0.5% maximum
6. BIAS TOWARD ACTION - scalpers make money on movement

🔥 DECISION FORMAT (JSON ONLY):
{{"action": "LONG/SHORT", "confidence": 75-95, "entry_price": {last_price:.6f}, "take_profit": 0.5-3.0, "stop_loss": 0.3-0.5, "hold_time": "30s-5min", "reasoning": "brief technical reason"}}

🚨 CRITICAL: If confidence < 70%, choose the direction with better risk/reward, don't WAIT!
Scalpers strike fast and often. Hesitation kills profits. MOVE NOW!"""
    
    return prompt
