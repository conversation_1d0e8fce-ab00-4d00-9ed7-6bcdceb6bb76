#!/usr/bin/env python3
"""
Enhanced System Validator with Configuration Backup and Recovery
Comprehensive system validation with error recovery and configuration management
"""

import sys
import os
import time
import json
import yaml
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedSystemValidator:
    """Enhanced system validator with configuration management"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / 'backups' / 'config_backups'
        self.validation_results = {}

        # Ensure backup directory exists
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        logger.info("🔧 Enhanced System Validator initialized")

    def validate_and_fix_configurations(self) -> bool:
        """Validate and fix configuration files with backup support"""
        print("🔧 ENHANCED CONFIGURATION VALIDATION & RECOVERY")
        print("=" * 60)

        success = True

        # Configuration files to validate
        config_files = [
            {
                'path': 'config/config.yaml',
                'type': 'yaml',
                'required': False,
                'template': self._get_default_config_yaml()
            },
            {
                'path': 'credentials.py',
                'type': 'python',
                'required': True,
                'template': self._get_default_credentials_py()
            },
            {
                'path': 'config/trading_config.py',
                'type': 'python',
                'required': True,
                'template': None
            }
        ]

        for config in config_files:
            result = self._validate_config_file(config)
            if not result:
                success = False

        # Create missing directories
        self._ensure_required_directories()

        # Validate system health
        self._validate_system_health()

        return success

    def _validate_config_file(self, config: Dict) -> bool:
        """Validate a single configuration file"""
        file_path = self.project_root / config['path']
        file_type = config['type']

        print(f"\n📋 Validating {config['path']}...")

        # Check if file exists
        if not file_path.exists():
            if config['required']:
                print(f"   ❌ Required file missing: {config['path']}")
                if config['template']:
                    return self._create_from_template(file_path, config['template'])
                return False
            else:
                print(f"   ⚠️ Optional file missing: {config['path']}")
                if config['template']:
                    return self._create_from_template(file_path, config['template'])
                return True

        # Backup existing file
        self._backup_config_file(file_path)

        # Validate file content
        try:
            if file_type == 'yaml':
                return self._validate_yaml_file(file_path)
            elif file_type == 'python':
                return self._validate_python_file(file_path)
            else:
                print(f"   ✅ File exists: {config['path']}")
                return True

        except Exception as e:
            print(f"   ❌ Validation error: {e}")
            return False

    def _validate_yaml_file(self, file_path: Path) -> bool:
        """Validate YAML file with encoding handling"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        yaml.safe_load(f)
                    print(f"   ✅ Valid YAML file (encoding: {encoding})")
                    return True
                except UnicodeDecodeError:
                    continue
                except yaml.YAMLError as e:
                    print(f"   ❌ YAML syntax error: {str(e)[:100]}")
                    return False

            print(f"   ❌ Could not decode file with any encoding")
            return False

        except Exception as e:
            print(f"   ❌ YAML validation error: {e}")
            return False

    def _validate_python_file(self, file_path: Path) -> bool:
        """Validate Python file with encoding handling"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()

                    # Try to compile
                    compile(content, str(file_path), 'exec')
                    print(f"   ✅ Valid Python file (encoding: {encoding})")
                    return True

                except UnicodeDecodeError:
                    continue
                except SyntaxError as e:
                    print(f"   ❌ Python syntax error: {str(e)[:100]}")
                    return False

            print(f"   ❌ Could not decode file with any encoding")
            return False

        except Exception as e:
            print(f"   ❌ Python validation error: {e}")
            return False

    def _backup_config_file(self, file_path: Path):
        """Create backup of configuration file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.name}_{timestamp}.backup"
            backup_path = self.backup_dir / backup_name

            shutil.copy2(file_path, backup_path)
            print(f"   💾 Backup created: {backup_name}")

        except Exception as e:
            print(f"   ⚠️ Backup failed: {e}")

    def _create_from_template(self, file_path: Path, template: str) -> bool:
        """Create configuration file from template"""
        try:
            # Ensure parent directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write template content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(template)

            print(f"   ✅ Created from template: {file_path.name}")
            return True

        except Exception as e:
            print(f"   ❌ Template creation failed: {e}")
            return False

    def _get_default_config_yaml(self) -> str:
        """Get default config.yaml template"""
        return """# Epinnox v6 Configuration
# Auto-generated configuration file

# Trading Settings
trading:
  initial_balance: 100.0
  max_positions: 3
  min_confidence: 0.65

# Risk Management
risk:
  max_portfolio_risk: 0.20
  max_position_size: 0.10
  max_leverage: 3.0
  stop_loss_pct: 0.05
  take_profit_pct: 0.10

# Symbols
symbols:
  - "BTC/USDT:USDT"
  - "ETH/USDT:USDT"
  - "DOGE/USDT:USDT"

# Timeframes
timeframe: "1m"

# ML/RL Settings
ml:
  use_rl: false
  use_lstm: false
  model_update_frequency: 100

# Logging
logging:
  level: "INFO"
  file: "logs/epinnox.log"
"""

    def _get_default_credentials_py(self) -> str:
        """Get default credentials.py template"""
        return '''"""
Epinnox v6 Trading Credentials
Configure your exchange API credentials here
"""

# HTX (Huobi) Exchange Credentials
htx = {
    'api_key': 'your_htx_api_key_here',
    'secret': 'your_htx_secret_here',
    'sandbox': False,  # Set to True for testing
    'enableRateLimit': True,
}

# Binance Exchange Credentials
binance = {
    'api_key': 'your_binance_api_key_here',
    'secret': 'your_binance_secret_here',
    'sandbox': False,  # Set to True for testing
    'enableRateLimit': True,
}

# OKX Exchange Credentials
okx = {
    'api_key': 'your_okx_api_key_here',
    'secret': 'your_okx_secret_here',
    'password': 'your_okx_passphrase_here',
    'sandbox': False,  # Set to True for testing
    'enableRateLimit': True,
}

# Default exchange to use
DEFAULT_EXCHANGE = 'htx'

# Credentials Manager Class
class CredentialsManager:
    """Manage trading credentials securely"""

    @staticmethod
    def get_credentials(exchange_name: str):
        """Get credentials for specified exchange"""
        credentials_map = {
            'htx': htx,
            'binance': binance,
            'okx': okx
        }

        return credentials_map.get(exchange_name.lower())

    @staticmethod
    def validate_credentials(exchange_name: str):
        """Validate that credentials are properly configured"""
        creds = CredentialsManager.get_credentials(exchange_name)

        if not creds:
            return False, f"No credentials found for {exchange_name}"

        required_fields = ['api_key', 'secret']
        if exchange_name.lower() == 'okx':
            required_fields.append('password')

        for field in required_fields:
            if not creds.get(field) or creds[field] == f'your_{exchange_name}_{field}_here':
                return False, f"Missing or default {field} for {exchange_name}"

        return True, "Credentials validated"
'''

    def _ensure_required_directories(self):
        """Ensure all required directories exist"""
        print(f"\n📁 Ensuring Required Directories...")

        required_dirs = [
            'logs', 'cache', 'data', 'config', 'screenshots',
            'test_results', 'backups', 'temp', 'exports',
            'backups/config_backups', 'backups/data_backups',
            'logs/trading', 'logs/system', 'data/market_data'
        ]

        created_dirs = []

        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_name)
                except Exception as e:
                    print(f"   ❌ Failed to create {dir_name}: {e}")

        if created_dirs:
            print(f"   ✅ Created directories: {', '.join(created_dirs)}")
        else:
            print(f"   ✅ All required directories exist")

    def _validate_system_health(self):
        """Validate overall system health"""
        print(f"\n🏥 System Health Validation...")

        health_checks = []

        # Check Python version
        python_version = sys.version_info
        if python_version >= (3, 8):
            health_checks.append("✅ Python version compatible")
        else:
            health_checks.append("❌ Python version too old")

        # Check critical imports
        critical_imports = ['PyQt5', 'ccxt', 'pandas', 'numpy', 'yaml']
        import_failures = []

        for module in critical_imports:
            try:
                __import__(module)
            except ImportError:
                import_failures.append(module)

        if not import_failures:
            health_checks.append("✅ All critical packages available")
        else:
            health_checks.append(f"❌ Missing packages: {', '.join(import_failures)}")

        # Check disk space
        try:
            import psutil
            disk_usage = psutil.disk_usage(str(self.project_root))
            free_gb = disk_usage.free / (1024**3)

            if free_gb >= 1.0:
                health_checks.append(f"✅ Sufficient disk space: {free_gb:.1f}GB")
            else:
                health_checks.append(f"⚠️ Low disk space: {free_gb:.1f}GB")
        except:
            health_checks.append("⚠️ Could not check disk space")

        # Display health status
        for check in health_checks:
            print(f"   {check}")

    def create_system_diagnostic_report(self) -> str:
        """Create comprehensive system diagnostic report"""
        report = f"""
🔍 EPINNOX v6 SYSTEM DIAGNOSTIC REPORT
{'=' * 60}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 SYSTEM INFORMATION:
   Platform: {sys.platform}
   Python: {sys.version.split()[0]}
   Working Directory: {self.project_root}

📁 DIRECTORY STRUCTURE:
"""

        # Check directory structure
        required_dirs = ['logs', 'cache', 'data', 'config', 'screenshots', 'test_results', 'backups']
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            status = "✅ EXISTS" if dir_path.exists() else "❌ MISSING"
            report += f"   {dir_name}: {status}\n"

        report += f"""
📋 CONFIGURATION FILES:
"""

        # Check configuration files
        config_files = ['config/config.yaml', 'credentials.py', 'config/trading_config.py']
        for config_file in config_files:
            file_path = self.project_root / config_file
            status = "✅ EXISTS" if file_path.exists() else "❌ MISSING"
            report += f"   {config_file}: {status}\n"

        report += f"""
💡 RECOMMENDATIONS:
   1. Run enhanced_system_validator.py to fix configuration issues
   2. Ensure all API credentials are properly configured
   3. Verify network connectivity for trading operations
   4. Check system resources (RAM, CPU, disk space)

🚀 NEXT STEPS:
   1. Fix any missing or invalid configuration files
   2. Test system with dependency checker
   3. Run comprehensive validation before live trading
"""

        return report

def main():
    """Main execution for enhanced system validation"""
    print("🔧 ENHANCED SYSTEM VALIDATOR")
    print("=" * 50)

    validator = EnhancedSystemValidator()

    # Run validation and fixes
    success = validator.validate_and_fix_configurations()

    # Generate diagnostic report
    report = validator.create_system_diagnostic_report()

    # Save report to file
    report_path = validator.project_root / 'system_diagnostic_report.txt'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"\n📊 Diagnostic report saved: {report_path}")
    print(report)

    if success:
        print("\n✅ SYSTEM VALIDATION COMPLETE")
        print("🚀 System ready for Epinnox operations")
    else:
        print("\n⚠️ SYSTEM VALIDATION ISSUES DETECTED")
        print("🔧 Review and fix issues before proceeding")

    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())