#!/usr/bin/env python3
"""
Comprehensive Dependency Installer for Epinnox v6
Automatically installs and validates all required dependencies
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DependencyInstaller:
    """Comprehensive dependency installer for Epinnox v6"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        # Map import names to package specs
        self.required_packages = {
            'PyQt5': 'PyQt5>=5.15.0',
            'ccxt': 'ccxt>=4.0.0',
            'pandas': 'pandas>=1.3.0',
            'numpy': 'numpy>=1.21.0',
            'requests': 'requests>=2.25.0',
            'yaml': 'PyYAML>=5.4.0',  # Import as 'yaml', install as 'PyYAML'
            'psutil': 'psutil>=5.8.0',
            'websockets': 'websockets>=10.0',
            'aiohttp': 'aiohttp>=3.8.0',
            'pytest_qt': 'pytest-qt>=4.0.0'  # Import as 'pytest_qt', install as 'pytest-qt'
        }
        
        logger.info("🔧 Dependency Installer initialized")
    
    def check_and_install_dependencies(self) -> bool:
        """Check and install all required dependencies"""
        print("🔧 EPINNOX v6 DEPENDENCY INSTALLER")
        print("=" * 50)
        
        # Check current status
        missing_packages = self._check_missing_packages()
        
        if not missing_packages:
            print("✅ All dependencies are already installed!")
            return True
        
        print(f"\n📦 Found {len(missing_packages)} missing packages:")
        for package in missing_packages:
            print(f"   ❌ {package}")
        
        # Install missing packages
        print(f"\n🔧 Installing missing dependencies...")
        success = self._install_packages(missing_packages)
        
        if success:
            # Verify installation
            print(f"\n✅ Verifying installation...")
            remaining_missing = self._check_missing_packages()
            
            if not remaining_missing:
                print("🎉 All dependencies successfully installed!")
                return True
            else:
                print(f"⚠️ {len(remaining_missing)} packages still missing:")
                for package in remaining_missing:
                    print(f"   ❌ {package}")
                return False
        else:
            print("❌ Dependency installation failed")
            return False
    
    def _check_missing_packages(self) -> list:
        """Check which packages are missing"""
        missing = []
        
        for package_name in self.required_packages.keys():
            try:
                __import__(package_name)
            except ImportError:
                missing.append(package_name)
        
        return missing
    
    def _install_packages(self, packages: list) -> bool:
        """Install the specified packages"""
        try:
            for package_name in packages:
                package_spec = self.required_packages[package_name]
                print(f"   📦 Installing {package_spec}...")
                
                # Use pip to install
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package_spec
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"   ✅ {package_name} installed successfully")
                else:
                    print(f"   ❌ {package_name} installation failed:")
                    print(f"      {result.stderr}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Installation error: {e}")
            return False
    
    def create_requirements_file(self):
        """Create requirements.txt file"""
        try:
            requirements_content = []
            
            for package_spec in self.required_packages.values():
                requirements_content.append(package_spec)
            
            # Add additional packages that might be useful
            additional_packages = [
                'matplotlib>=3.3.0',
                'seaborn>=0.11.0',
                'scikit-learn>=1.0.0',
                'ta>=0.7.0',  # Technical analysis library
                'python-dotenv>=0.19.0'  # Environment variables
            ]
            
            requirements_content.extend(additional_packages)
            
            # Write requirements.txt
            requirements_path = self.project_root / 'requirements.txt'
            with open(requirements_path, 'w') as f:
                f.write('\n'.join(requirements_content))
            
            print(f"📄 Requirements file created: {requirements_path}")
            
        except Exception as e:
            print(f"⚠️ Could not create requirements file: {e}")
    
    def install_from_requirements(self) -> bool:
        """Install dependencies from requirements.txt"""
        try:
            requirements_path = self.project_root / 'requirements.txt'
            
            if not requirements_path.exists():
                print("❌ requirements.txt not found")
                return False
            
            print("📦 Installing from requirements.txt...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_path)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ All requirements installed successfully")
                return True
            else:
                print("❌ Requirements installation failed:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Requirements installation error: {e}")
            return False
    
    def upgrade_pip(self) -> bool:
        """Upgrade pip to latest version"""
        try:
            print("🔧 Upgrading pip...")
            
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ pip upgraded successfully")
                return True
            else:
                print("⚠️ pip upgrade failed (continuing anyway)")
                return True  # Not critical
                
        except Exception as e:
            print(f"⚠️ pip upgrade error: {e}")
            return True  # Not critical
    
    def validate_installation(self) -> bool:
        """Validate that all packages are properly installed"""
        print("\n🔍 VALIDATING INSTALLATION")
        print("-" * 30)
        
        validation_results = {}
        
        for package_name in self.required_packages.keys():
            try:
                module = __import__(package_name)
                
                # Try to get version if available
                version = "unknown"
                if hasattr(module, '__version__'):
                    version = module.__version__
                elif hasattr(module, 'version'):
                    version = module.version
                
                print(f"   ✅ {package_name}: {version}")
                validation_results[package_name] = True
                
            except ImportError as e:
                print(f"   ❌ {package_name}: Import failed - {e}")
                validation_results[package_name] = False
        
        # Summary
        successful = sum(validation_results.values())
        total = len(validation_results)
        
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   ✅ Successful: {successful}/{total}")
        print(f"   ❌ Failed: {total - successful}/{total}")
        print(f"   📈 Success Rate: {(successful/total)*100:.1f}%")
        
        return successful == total
    
    def run_complete_installation(self) -> bool:
        """Run complete dependency installation process"""
        print("🚀 COMPLETE DEPENDENCY INSTALLATION PROCESS")
        print("=" * 60)
        
        # Step 1: Upgrade pip
        self.upgrade_pip()
        
        # Step 2: Create requirements file
        self.create_requirements_file()
        
        # Step 3: Check and install dependencies
        install_success = self.check_and_install_dependencies()
        
        # Step 4: Validate installation
        validation_success = self.validate_installation()
        
        # Final result
        overall_success = install_success and validation_success
        
        print(f"\n🎯 INSTALLATION COMPLETE")
        if overall_success:
            print("✅ All dependencies successfully installed and validated")
            print("🚀 Epinnox v6 is ready for deployment!")
        else:
            print("❌ Some dependencies failed to install")
            print("🛠️ Manual intervention may be required")
        
        return overall_success

def main():
    """Main execution for dependency installation"""
    installer = DependencyInstaller()
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == '--requirements':
            # Install from requirements.txt
            installer.create_requirements_file()
            success = installer.install_from_requirements()
        elif sys.argv[1] == '--validate':
            # Just validate current installation
            success = installer.validate_installation()
        else:
            print("Usage: python dependency_installer.py [--requirements|--validate]")
            return 1
    else:
        # Run complete installation
        success = installer.run_complete_installation()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
